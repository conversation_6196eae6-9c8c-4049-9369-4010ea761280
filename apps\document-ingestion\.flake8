[flake8]
max-line-length = 120
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .env,
    *.pyc,
    *.pyo,
    *.pyd,
    .pytest_cache,
    .coverage,
    htmlcov,
    .tox,
    .eggs,
    *.egg,
    dist,
    build,
    .mypy_cache,
    .ruff_cache

# Simple syntax error checking only
ignore = 
    E501,
    W503,
    E203,
    F401,
    F403,
    F405,
    E722,
    W291,
    W293,
    E302,
    E303,
    E305,
    E306,
    W391,
    E402,
    E731,
    E741,
    E742,
    E743,
    W291,
    W292,
    W293,
    W391,

# Only check for critical syntax errors
select = 
    E999,
    F821,
    F822,
    F823,
    F831,
    F841,
    F901,

# Show source code for each error
show-source = True

# Show statistics
statistics = True

# Count errors
count = True 