"""
Supabase Client Utility for Multi-Tenant Document Ingestion Pipeline

This module provides a reusable Supabase client with connection management,
error handling, and multi-tenant support for the document ingestion Lambda function.
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json

from supabase import create_client, Client
import vecs
from tenacity import retry, stop_after_attempt, wait_exponential
import numpy as np

logger = logging.getLogger(__name__)

class SupabaseClientError(Exception):
    """Custom exception for Supabase client errors"""
    pass

class SupabaseClient:
    """
    Multi-tenant Supabase client for document ingestion pipeline
    
    Provides methods for:
    - Document metadata management
    - Vector embeddings storage and retrieval
    - Multi-tenant data isolation
    - Error handling and retries
    """
    
    def __init__(self, 
                 supabase_url: Optional[str] = None,
                 supabase_key: Optional[str] = None,
                 use_service_role: bool = True):
        """
        Initialize Supabase client
        
        Args:
            supabase_url: Supabase project URL (defaults to env var)
            supabase_key: Supabase key (defaults to env var)
            use_service_role: Whether to use service role key (bypasses RLS)
        """
        self.supabase_url = supabase_url or os.getenv('SUPABASE_URL')
        
        if use_service_role:
            self.supabase_key = supabase_key or os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        else:
            self.supabase_key = supabase_key or os.getenv('SUPABASE_ANON_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise SupabaseClientError("Supabase URL and key must be provided")
        
        self.client: Client = create_client(self.supabase_url, self.supabase_key)
        self.vecs_client = None
        self._initialize_vecs_client()
    
    def _initialize_vecs_client(self):
        """Initialize vecs client for vector operations"""
        try:
            # Extract database connection info from Supabase URL
            db_connection = self._get_db_connection_string()
            self.vecs_client = vecs.create_client(db_connection)
        except Exception as e:
            logger.warning(f"Failed to initialize vecs client: {e}")
            self.vecs_client = None
    
    def _get_db_connection_string(self) -> str:
        """Get PostgreSQL connection string from Supabase URL"""
        # Convert Supabase URL to PostgreSQL connection string
        # Format: postgresql://postgres:[password]@[host]:5432/postgres
        url_parts = self.supabase_url.replace('https://', '').split('.')
        project_id = url_parts[0]
        
        # Use service role key as password for direct DB connection
        return f"postgresql://postgres:{self.supabase_key}@db.{project_id}.supabase.co:5432/postgres"
    
    def set_user_context(self, user_id: str):
        """Set user context for Row Level Security"""
        try:
            self.client.rpc('set_config', {
                'setting_name': 'app.current_user_id',
                'new_value': user_id,
                'is_local': True
            })
        except Exception as e:
            logger.warning(f"Failed to set user context: {e}")
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def create_document(self, 
                       user_id: str,
                       document_id: str,
                       s3_key: str,
                       original_filename: str,
                       column_names: List[str],
                       total_rows: int) -> Dict[str, Any]:
        """
        Create a new document record
        
        Args:
            user_id: User identifier for multi-tenant isolation
            document_id: Unique document identifier
            s3_key: S3 object key
            original_filename: Original CSV filename
            column_names: List of CSV column names
            total_rows: Total number of rows in CSV
            
        Returns:
            Created document record
        """
        try:
            self.set_user_context(user_id)
            
            document_data = {
                'user_id': user_id,
                'document_id': document_id,
                's3_key': s3_key,
                'original_filename': original_filename,
                'column_names': column_names,
                'total_rows': total_rows,
                'upload_status': 'pending'
            }
            
            result = self.client.table('documents').insert(document_data).execute()
            
            if result.data:
                logger.info(f"Created document record: {document_id}")
                return result.data[0]
            else:
                raise SupabaseClientError(f"Failed to create document: {result}")
                
        except Exception as e:
            logger.error(f"Error creating document {document_id}: {e}")
            raise SupabaseClientError(f"Failed to create document: {e}")
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def update_document_status(self, 
                              user_id: str,
                              document_id: str,
                              status: str,
                              error_message: Optional[str] = None) -> Dict[str, Any]:
        """
        Update document processing status
        
        Args:
            user_id: User identifier
            document_id: Document identifier
            status: New status (pending, processing, completed, failed)
            error_message: Error message if status is failed
            
        Returns:
            Updated document record
        """
        try:
            self.set_user_context(user_id)
            
            update_data = {
                'upload_status': status,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            if status == 'processing':
                update_data['processing_started_at'] = datetime.utcnow().isoformat()
            elif status in ['completed', 'failed']:
                update_data['processing_completed_at'] = datetime.utcnow().isoformat()
            
            if error_message:
                update_data['error_message'] = error_message
            
            result = self.client.table('documents').update(update_data).eq('user_id', user_id).eq('document_id', document_id).execute()
            
            if result.data:
                logger.info(f"Updated document status: {document_id} -> {status}")
                return result.data[0]
            else:
                raise SupabaseClientError(f"Failed to update document status: {result}")
                
        except Exception as e:
            logger.error(f"Error updating document status {document_id}: {e}")
            raise SupabaseClientError(f"Failed to update document status: {e}")
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def batch_insert_embeddings(self, 
                               user_id: str,
                               document_id: str,
                               embeddings_data: List[Dict[str, Any]]) -> bool:
        """
        Batch insert product embeddings
        
        Args:
            user_id: User identifier
            document_id: Document identifier
            embeddings_data: List of embedding records with structure:
                {
                    'row_index': int,
                    'embedding_vector': List[float],
                    'product_data': Dict[str, Any],
                    'searchable_text': str
                }
                
        Returns:
            True if successful
        """
        try:
            self.set_user_context(user_id)
            
            # Prepare batch data
            batch_data = []
            for item in embeddings_data:
                record = {
                    'document_id': document_id,
                    'user_id': user_id,
                    'row_index': item['row_index'],
                    'embedding_vector': item['embedding_vector'],
                    'product_data': item['product_data'],
                    'searchable_text': item['searchable_text']
                }
                batch_data.append(record)
            
            # Insert in batches of 100 to avoid payload limits
            batch_size = 100
            for i in range(0, len(batch_data), batch_size):
                batch = batch_data[i:i + batch_size]
                result = self.client.table('product_embeddings').insert(batch).execute()
                
                if not result.data:
                    raise SupabaseClientError(f"Failed to insert batch {i//batch_size + 1}: {result}")
            
            logger.info(f"Inserted {len(embeddings_data)} embeddings for document {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting embeddings for document {document_id}: {e}")
            raise SupabaseClientError(f"Failed to insert embeddings: {e}")
    
    def search_similar_products(self, 
                               user_id: str,
                               query_embedding: List[float],
                               similarity_threshold: float = 0.7,
                               max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search for similar products using vector similarity
        
        Args:
            user_id: User identifier
            query_embedding: Query embedding vector
            similarity_threshold: Minimum similarity score
            max_results: Maximum number of results
            
        Returns:
            List of similar products with similarity scores
        """
        try:
            self.set_user_context(user_id)
            
            result = self.client.rpc('find_similar_products', {
                'query_embedding': query_embedding,
                'target_user_id': user_id,
                'similarity_threshold': similarity_threshold,
                'max_results': max_results
            }).execute()
            
            if result.data:
                logger.info(f"Found {len(result.data)} similar products for user {user_id}")
                return result.data
            else:
                return []
                
        except Exception as e:
            logger.error(f"Error searching similar products for user {user_id}: {e}")
            raise SupabaseClientError(f"Failed to search similar products: {e}")
    
    def get_document_stats(self, user_id: str) -> Dict[str, int]:
        """
        Get document processing statistics for a user
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary with document statistics
        """
        try:
            self.set_user_context(user_id)
            
            result = self.client.rpc('get_document_stats', {
                'target_user_id': user_id
            }).execute()
            
            if result.data and len(result.data) > 0:
                stats = result.data[0]
                return {
                    'total_documents': stats.get('total_documents', 0),
                    'total_products': stats.get('total_products', 0),
                    'completed_documents': stats.get('completed_documents', 0),
                    'failed_documents': stats.get('failed_documents', 0),
                    'pending_documents': stats.get('pending_documents', 0)
                }
            else:
                return {
                    'total_documents': 0,
                    'total_products': 0,
                    'completed_documents': 0,
                    'failed_documents': 0,
                    'pending_documents': 0
                }
                
        except Exception as e:
            logger.error(f"Error getting document stats for user {user_id}: {e}")
            raise SupabaseClientError(f"Failed to get document stats: {e}")
    
    def health_check(self) -> bool:
        """
        Perform health check on Supabase connection
        
        Returns:
            True if connection is healthy
        """
        try:
            # Simple query to test connection
            self.client.table('documents').select('id').limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Supabase health check failed: {e}")
            return False
