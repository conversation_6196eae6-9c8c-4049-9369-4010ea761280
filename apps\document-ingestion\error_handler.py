"""
Error Handling and Retry Utilities for Document Ingestion Pipeline

This module provides comprehensive error handling, retry logic, and monitoring
utilities for the document ingestion Lambda function.
"""

import logging
import time
from typing import Any, Callable, Dict, List, Optional, Type
from functools import wraps
import traceback

from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)
import openai
import pandas as pd
from botocore.exceptions import ClientError, BotoCoreError
from supabase_client import SupabaseClientError

logger = logging.getLogger(__name__)


class RetryableError(Exception):
    """Base class for errors that should be retried"""


class NonRetryableError(Exception):
    """Base class for errors that should not be retried"""


class CSVValidationError(NonRetryableError):
    """Error for invalid CSV format or content"""


class S3AccessError(RetryableError):
    """Error for S3 access issues"""


class OpenAIAPIError(RetryableError):
    """Error for OpenAI API issues"""


class SupabaseError(RetryableError):
    """Error for Supabase operations"""


def classify_error(error: Exception) -> Type[Exception]:
    """
    Classify an error as retryable or non-retryable

    Args:
        error: The exception to classify

    Returns:
        Error class (RetryableError or NonRetryableError)
    """
    # OpenAI API errors
    if isinstance(error, openai.RateLimitError):
        return RetryableError
    elif isinstance(error, openai.APITimeoutError):
        return RetryableError
    elif isinstance(error, openai.APIConnectionError):
        return RetryableError
    elif isinstance(error, openai.InternalServerError):
        return RetryableError
    elif isinstance(error, openai.AuthenticationError):
        return NonRetryableError
    elif isinstance(error, openai.BadRequestError):
        return NonRetryableError

    # AWS/Boto3 errors
    elif isinstance(error, ClientError):
        error_code = error.response.get("Error", {}).get("Code", "")
        if error_code in ["Throttling", "ThrottlingException", "ServiceUnavailable"]:
            return RetryableError
        elif error_code in ["AccessDenied", "InvalidBucketName", "NoSuchBucket"]:
            return NonRetryableError
        else:
            return RetryableError  # Default to retryable for unknown AWS errors

    elif isinstance(error, BotoCoreError):
        return RetryableError

    # Supabase errors
    elif isinstance(error, SupabaseClientError):
        return RetryableError

    # CSV/Data validation errors
    elif isinstance(error, (ValueError, KeyError)) and "csv" in str(error).lower():
        return NonRetryableError

    # Default to non-retryable for unknown errors
    return NonRetryableError


def with_retry(
    max_attempts: int = 3,
    min_wait: int = 1,
    max_wait: int = 60,
    exponential_base: int = 2,
):
    """
    Decorator for adding retry logic to functions

    Args:
        max_attempts: Maximum number of retry attempts
        min_wait: Minimum wait time between retries (seconds)
        max_wait: Maximum wait time between retries (seconds)
        exponential_base: Base for exponential backoff
    """

    def decorator(func: Callable) -> Callable:
        @retry(
            stop=stop_after_attempt(max_attempts),
            wait=wait_exponential(multiplier=min_wait, min=min_wait, max=max_wait),
            retry=retry_if_exception_type(RetryableError),
            before_sleep=before_sleep_log(logger, logging.WARNING),
            reraise=True,
        )
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Classify error and re-raise as appropriate type
                error_type = classify_error(e)
                if error_type == RetryableError:
                    raise RetryableError(
                        f"Retryable error in {func.__name__}: {e}"
                    ) from e
                else:
                    raise NonRetryableError(
                        f"Non-retryable error in {func.__name__}: {e}"
                    ) from e

        return wrapper

    return decorator


def log_error_details(
    error: Exception, context: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Log detailed error information for debugging and monitoring

    Args:
        error: The exception that occurred
        context: Additional context information

    Returns:
        Dictionary with error details
    """
    error_details = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "error_module": getattr(error, "__module__", "unknown"),
        "timestamp": time.time(),
        "traceback": traceback.format_exc(),
    }

    if context:
        error_details["context"] = context

    # Add specific details for known error types
    if isinstance(error, ClientError):
        error_details["aws_error_code"] = error.response.get("Error", {}).get("Code")
        error_details["aws_error_message"] = error.response.get("Error", {}).get(
            "Message"
        )
        error_details["aws_request_id"] = error.response.get(
            "ResponseMetadata", {}
        ).get("RequestId")

    elif hasattr(error, "response") and hasattr(error.response, "status_code"):
        error_details["http_status_code"] = error.response.status_code
        error_details["http_response"] = getattr(error.response, "text", "")

    logger.error(f"Error details: {error_details}")
    return error_details


def validate_csv_data(
    df, required_columns: Optional[List[str]] = None, max_rows: int = 100000
) -> bool:
    """
    Comprehensive CSV data validation with malformed data handling

    Args:
        df: Pandas DataFrame
        required_columns: List of required column names
        max_rows: Maximum allowed rows for processing

    Returns:
        True if valid

    Raises:
        CSVValidationError: If validation fails
    """
    try:
        # Check if DataFrame is empty
        if df.empty:
            raise CSVValidationError("CSV file is empty")

        # Check row count limits
        if len(df) > max_rows:
            raise CSVValidationError(
                f"CSV file too large: {len(df)} rows (max: {max_rows})"
            )

        # Check for required columns
        if required_columns:
            missing_columns = set(required_columns) - set(df.columns)
            if missing_columns:
                raise CSVValidationError(f"Missing required columns: {missing_columns}")

        # Check for malformed column names
        invalid_columns = []
        for col in df.columns:
            if not isinstance(col, str) or not col.strip():
                invalid_columns.append(col)
            elif len(col.strip()) > 255:  # Column name too long
                invalid_columns.append(col)

        if invalid_columns:
            raise CSVValidationError(f"Invalid column names found: {invalid_columns}")

        # Check for completely empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            logger.warning(
                f"Found {empty_rows} completely empty rows - will be skipped"
            )

        # Check for rows with all empty strings
        string_empty_rows = (
            df.astype(str).replace("", None).isnull().all(axis=1)
        ).sum()
        if string_empty_rows > 0:
            logger.warning(f"Found {string_empty_rows} rows with only empty strings")

        # Check for duplicate rows
        duplicate_rows = df.duplicated().sum()
        if duplicate_rows > 0:
            logger.warning(f"Found {duplicate_rows} duplicate rows")

        # Check for columns with all null values
        null_columns = df.columns[df.isnull().all()].tolist()
        if null_columns:
            logger.warning(f"Columns with all null values: {null_columns}")

        # Check data types and encoding issues
        problematic_columns = []
        for column in df.columns:
            try:
                if df[column].dtype == "object":
                    # Check for encoding issues
                    df[column].astype(str)

                    # Check for extremely long values
                    max_length = df[column].astype(str).str.len().max()
                    if max_length > 10000:  # Very long text values
                        logger.warning(
                            f"Column '{column}' has very long values (max: {max_length} chars)"
                        )

                    # Check for null-like strings
                    null_like_values = [
                        "null",
                        "NULL",
                        "None",
                        "NONE",
                        "n/a",
                        "N/A",
                        "#N/A",
                    ]
                    null_like_count = df[column].isin(null_like_values).sum()
                    if null_like_count > 0:
                        logger.info(
                            f"Column '{column}' has {null_like_count} null-like string values"
                        )

            except (UnicodeDecodeError, ValueError) as e:
                problematic_columns.append(f"{column}: {str(e)}")

        if problematic_columns:
            raise CSVValidationError(
                f"Data type/encoding issues in columns: {problematic_columns}"
            )

        # Check for suspicious patterns
        warnings = []

        # Check for columns with only one unique value
        single_value_columns = []
        for column in df.columns:
            unique_count = df[column].nunique()
            if unique_count == 1:
                single_value_columns.append(column)

        if single_value_columns:
            warnings.append(
                f"Columns with only one unique value: {single_value_columns}"
            )

        # Check for columns with very high cardinality (might be IDs)
        high_cardinality_columns = []
        for column in df.columns:
            unique_count = df[column].nunique()
            if unique_count > len(df) * 0.9:  # More than 90% unique values
                high_cardinality_columns.append(f"{column} ({unique_count} unique)")

        if high_cardinality_columns:
            warnings.append(f"High cardinality columns: {high_cardinality_columns}")

        # Log warnings
        for warning in warnings:
            logger.warning(warning)

        # Calculate data quality score
        total_cells = len(df) * len(df.columns)
        null_cells = df.isnull().sum().sum()
        data_quality_score = (
            (total_cells - null_cells) / total_cells if total_cells > 0 else 0
        )

        logger.info(
            f"CSV validation passed: {len(df)} rows, {len(df.columns)} columns, "
            f"data quality score: {data_quality_score:.2%}"
        )

        # Warn if data quality is low
        if data_quality_score < 0.5:
            logger.warning(
                f"Low data quality score: {data_quality_score:.2%} - "
                f"consider data cleaning before processing"
            )

        return True

    except Exception as e:
        if isinstance(e, CSVValidationError):
            raise
        else:
            raise CSVValidationError(f"CSV validation failed: {e}")


def clean_csv_data(df) -> Any:
    """
    Clean CSV data by handling common malformed data issues

    Args:
        df: Pandas DataFrame

    Returns:
        Cleaned DataFrame
    """

    logger.info(f"Cleaning CSV data: {len(df)} rows, {len(df.columns)} columns")

    # Create a copy to avoid modifying original
    cleaned_df = df.copy()

    # Remove completely empty rows
    cleaned_df = cleaned_df.dropna(how="all")

    # Remove rows where all values are empty strings
    string_mask = ~(cleaned_df.astype(str).replace("", None).isnull().all(axis=1))
    cleaned_df = cleaned_df[string_mask]

    # Clean column names
    cleaned_df.columns = [str(col).strip() for col in cleaned_df.columns]

    # Replace null-like strings with actual NaN
    null_like_values = ["null", "NULL", "None", "NONE", "n/a", "N/A", "#N/A", ""]
    cleaned_df = cleaned_df.replace(null_like_values, pd.NA)

    # Remove columns that are completely empty
    cleaned_df = cleaned_df.dropna(axis=1, how="all")

    # Trim whitespace from string columns
    for column in cleaned_df.columns:
        if cleaned_df[column].dtype == "object":
            cleaned_df[column] = cleaned_df[column].astype(str).str.strip()
            # Replace empty strings with NaN
            cleaned_df[column] = cleaned_df[column].replace("", pd.NA)

    # Remove duplicate rows
    initial_rows = len(cleaned_df)
    cleaned_df = cleaned_df.drop_duplicates()
    removed_duplicates = initial_rows - len(cleaned_df)

    if removed_duplicates > 0:
        logger.info(f"Removed {removed_duplicates} duplicate rows")

    logger.info(
        f"Data cleaning completed: {len(cleaned_df)} rows, {len(cleaned_df.columns)} columns"
    )

    return cleaned_df


def safe_execute(func: Callable, *args, **kwargs) -> Dict[str, Any]:
    """
    Safely execute a function with comprehensive error handling

    Args:
        func: Function to execute
        *args: Function arguments
        **kwargs: Function keyword arguments

    Returns:
        Dictionary with execution result and error information
    """
    result = {
        "success": False,
        "result": None,
        "error": None,
        "error_details": None,
        "execution_time": 0,
    }

    start_time = time.time()

    try:
        result["result"] = func(*args, **kwargs)
        result["success"] = True
        logger.info(f"Successfully executed {func.__name__}")

    except Exception as e:
        result["error"] = str(e)
        result["error_details"] = log_error_details(
            e,
            {
                "function": func.__name__,
                "args": str(args)[:200],  # Truncate long arguments
                "kwargs": str(kwargs)[:200],
            },
        )
        logger.error(f"Failed to execute {func.__name__}: {e}")

    finally:
        result["execution_time"] = time.time() - start_time

    return result


class ErrorMetrics:
    """Simple error metrics collector for monitoring"""

    def __init__(self):
        self.error_counts = {}
        self.total_operations = 0
        self.failed_operations = 0

    def record_error(self, error_type: str):
        """Record an error occurrence"""
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        self.failed_operations += 1
        self.total_operations += 1

    def record_success(self):
        """Record a successful operation"""
        self.total_operations += 1

    def get_error_rate(self) -> float:
        """Get overall error rate"""
        if self.total_operations == 0:
            return 0.0
        return self.failed_operations / self.total_operations

    def get_metrics(self) -> Dict[str, Any]:
        """Get all metrics"""
        return {
            "total_operations": self.total_operations,
            "failed_operations": self.failed_operations,
            "error_rate": self.get_error_rate(),
            "error_counts": self.error_counts.copy(),
        }

    def reset(self):
        """Reset all metrics"""
        self.error_counts.clear()
        self.total_operations = 0
        self.failed_operations = 0


# Global error metrics instance
error_metrics = ErrorMetrics()
