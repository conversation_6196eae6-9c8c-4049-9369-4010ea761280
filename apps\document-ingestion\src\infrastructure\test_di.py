"""
Test script for dependency injection container

This script verifies that the dependency injection container is properly
configured and all dependencies can be resolved.
"""

import os
import sys
from pathlib import Path

# Add the apps/document-ingestion directory to Python path
current_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(current_dir))

# Set environment variables for testing
os.environ.setdefault('OPENAI_API_KEY', 'test-key')
os.environ.setdefault('SUPABASE_URL', 'https://test.supabase.co')
os.environ.setdefault('SUPABASE_SERVICE_ROLE_KEY', 'test-key')

try:
    # Import using absolute imports
    from apps.document_ingestion.src.infrastructure.dependency_injection import ApplicationContainer
except ImportError:
    try:
        # Try alternative import path
        sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent.parent))
        from apps.document_ingestion.src.infrastructure.dependency_injection import ApplicationContainer
    except ImportError as e:
        print(f"Import error: {e}")
        print("Unable to import ApplicationContainer")
        print("Please ensure you're running from the project root and all dependencies are installed")
        sys.exit(1)


def test_dependency_injection():
    """Test that all dependencies can be resolved"""
    
    try:
        # Create container
        container = ApplicationContainer()
        
        print("✅ Container created successfully")
        
        # Test configuration
        config = container.config()
        print(f"✅ Configuration resolved: {type(config).__name__}")
        
        # Test logger
        logger = container.logger()
        print(f"✅ Logger resolved: {type(logger).__name__}")
        
        # Test metrics collector
        metrics = container.metrics_collector()
        print(f"✅ Metrics collector resolved: {type(metrics).__name__}")
        
        # Test domain services
        text_generator = container.searchable_text_generator()
        print(f"✅ Text generator resolved: {type(text_generator).__name__}")
        
        validation_service = container.document_validation_service()
        print(f"✅ Validation service resolved: {type(validation_service).__name__}")
        
        security_service = container.security_service()
        print(f"✅ Security service resolved: {type(security_service).__name__}")
        
        # Test infrastructure services
        document_repo = container.document_repository()
        print(f"✅ Document repository resolved: {type(document_repo).__name__}")
        
        embedding_repo = container.embedding_repository()
        print(f"✅ Embedding repository resolved: {type(embedding_repo).__name__}")
        
        file_storage = container.file_storage()
        print(f"✅ File storage resolved: {type(file_storage).__name__}")
        
        embedding_service = container.embedding_service()
        print(f"✅ Embedding service resolved: {type(embedding_service).__name__}")
        
        csv_processor = container.csv_processor()
        print(f"✅ CSV processor resolved: {type(csv_processor).__name__}")
        
        event_publisher = container.event_publisher()
        print(f"✅ Event publisher resolved: {type(event_publisher).__name__}")
        
        # Test application services
        embedding_gen_service = container.embedding_generation_service()
        print(f"✅ Embedding generation service resolved: {type(embedding_gen_service).__name__}")
        
        # Test use case
        use_case = container.process_document_use_case()
        print(f"✅ Process document use case resolved: {type(use_case).__name__}")
        
        print("\n🎉 All dependencies resolved successfully!")
        print("🏗️ Clean architecture with dependency injection is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error resolving dependencies: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_singleton_behavior():
    """Test that singleton providers return the same instance"""
    
    try:
        container = ApplicationContainer()
        
        # Test that singletons return the same instance
        config1 = container.config()
        config2 = container.config()
        assert config1 is config2, "Config should be singleton"
        
        logger1 = container.logger()
        logger2 = container.logger()
        assert logger1 is logger2, "Logger should be singleton"
        
        use_case1 = container.process_document_use_case()
        use_case2 = container.process_document_use_case()
        assert use_case1 is use_case2, "Use case should be singleton"
        
        print("✅ Singleton behavior verified")
        return True
        
    except Exception as e:
        print(f"❌ Singleton test failed: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Testing Dependency Injection Container")
    print("=" * 50)
    
    success = True
    
    print("\n1. Testing dependency resolution...")
    success &= test_dependency_injection()
    
    print("\n2. Testing singleton behavior...")
    success &= test_singleton_behavior()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Dependency injection is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the configuration.")
        sys.exit(1)
