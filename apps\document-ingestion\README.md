# Document Ingestion Pipeline - Clean Architecture

A multi-tenant CSV document ingestion pipeline for the EzyChat RAG-powered sales assistant, built with **Clean Architecture** principles and **dependency injection**.

## 🏗️ Architecture Overview

This project follows **Clean Architecture** patterns with proper separation of concerns:

```
src/
├── domain/           # Business entities and rules (innermost layer)
│   ├── entities.py   # Document, ProductData, EmbeddingVector
│   ├── interfaces.py # Repository and service interfaces
│   └── services.py   # Domain services
├── application/      # Use cases and orchestration
│   └── use_cases.py  # ProcessDocumentUseCase
├── infrastructure/   # External dependencies (outermost layer)
│   ├── configuration.py
│   ├── logging.py
│   ├── metrics.py
│   ├── repositories.py      # Supabase implementations
│   ├── storage.py           # S3 implementations
│   ├── services.py          # OpenAI implementations
│   ├── processors.py        # CSV processing
│   ├── events.py            # Event publishing
│   └── dependency_injection.py
└── presentation/     # Controllers and handlers
    └── lambda_handler.py
```

## 🔧 Technology Stack

- **Runtime:** Python 3.12
- **Architecture:** Clean Architecture with dependency injection
- **DI Framework:** [dependency-injector](https://pypi.org/project/dependency-injector/)
- **Vector Store:** Supabase pgvector
- **Embeddings:** OpenAI text-embedding-3-small
- **Storage:** AWS S3, Supabase
- **Processing:** Pandas for CSV handling
- **Containerization:** Docker with AWS Lambda base image

## 🚀 Key Features

### Clean Architecture Benefits
- ✅ **Separation of Concerns:** Clear boundaries between layers
- ✅ **Dependency Inversion:** Business logic doesn't depend on infrastructure
- ✅ **Testability:** Easy to unit test with mocked dependencies
- ✅ **Maintainability:** Changes in one layer don't affect others
- ✅ **Extensibility:** New features can be added without breaking existing code

### Dependency Injection Benefits
- ✅ **Loose Coupling:** Components depend on abstractions, not concretions
- ✅ **Configuration Management:** Centralized dependency configuration
- ✅ **Singleton Management:** Automatic lifecycle management
- ✅ **Testing Support:** Easy to inject mocks for testing
- ✅ **Runtime Flexibility:** Can swap implementations without code changes

### Business Features
- ✅ **Multi-tenant isolation** with user_id-based partitioning
- ✅ **Comprehensive error handling** and retry mechanisms
- ✅ **Structured logging** with CloudWatch integration
- ✅ **Configurable embeddings** (model and dimensions)
- ✅ **Batch processing** for OpenAI API efficiency
- ✅ **CSV validation and cleaning** with data quality scoring
- ✅ **S3 file lifecycle management** (uploaded → processed/failed)

## 📋 Prerequisites

- Python 3.12+
- AWS CLI configured
- OpenAI API key
- Supabase project with pgvector enabled

## 🛠️ Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. **Test dependency injection:**
   ```bash
   python src/infrastructure/test_di.py
   ```

## 🔧 Configuration

The application uses environment variables for configuration. See `.env.example` for all available options:

### Required Configuration
```bash
OPENAI_API_KEY=sk-your-openai-api-key-here
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

### Optional Configuration
```bash
# Environment
ENVIRONMENT=dev
LOG_LEVEL=INFO

# OpenAI
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=512

# Processing Limits
MAX_FILE_SIZE_BYTES=52428800
MAX_ROWS=100000
BATCH_SIZE=100

# Dependency Injection
DI_AUTO_WIRE=true
DI_STRICT_MODE=false
```

## 🏃‍♂️ Usage

### Lambda Deployment
The main entry point is `app.py` which delegates to the clean architecture handler:

```python
from src.presentation.lambda_handler import lambda_handler as clean_lambda_handler

def lambda_handler(event, context):
    return clean_lambda_handler(event, context)
```

### Local Testing
```python
from src.infrastructure.dependency_injection import ApplicationContainer
from src.application.use_cases import ProcessDocumentCommand

# Initialize container
container = ApplicationContainer()
container.wire(modules=[__name__])

# Get use case
use_case = container.process_document_use_case()

# Execute
command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")
result = await use_case.execute(command)
```

## 🧪 Testing

### Unit Testing
The clean architecture makes unit testing straightforward:

```python
from unittest.mock import Mock
from src.application.use_cases import ProcessDocumentUseCase

# Mock all dependencies
mock_repo = Mock()
mock_storage = Mock()
# ... other mocks

# Create use case with mocks
use_case = ProcessDocumentUseCase(
    document_repo=mock_repo,
    file_storage=mock_storage,
    # ... other dependencies
)

# Test business logic without external dependencies
```

### Integration Testing
```bash
# Test dependency injection
python src/infrastructure/test_di.py

# Test with real AWS/Supabase (requires credentials)
python -m pytest tests/integration/
```

## 📊 Monitoring

### Structured Logging
All logs are structured JSON for CloudWatch:
```json
{
  "timestamp": "2025-08-07T10:30:00Z",
  "level": "INFO",
  "message": "Document processing completed",
  "user_id": "user123",
  "document_id": "doc456",
  "processed_rows": 1000,
  "duration_ms": 45000
}
```

### Metrics Collection
- Processing times
- Success/failure rates
- Error categorization
- Resource utilization

## 🔄 Data Flow

1. **S3 Upload:** CSV uploaded to `{user_id}/uploaded/{filename}.csv`
2. **Lambda Trigger:** S3 event triggers Lambda function
3. **Dependency Injection:** Container wires all dependencies
4. **Use Case Execution:** ProcessDocumentUseCase orchestrates the flow
5. **CSV Processing:** Parse, validate, and clean data
6. **Embedding Generation:** OpenAI generates vector embeddings
7. **Storage:** Save to Supabase with pgvector
8. **File Management:** Move to processed/failed folder
9. **Event Publishing:** Publish completion events

## 🛡️ Security

- **Multi-tenant isolation:** Complete data separation by user_id
- **Input validation:** CSV schema and content validation
- **Error handling:** Comprehensive error classification
- **Audit logging:** All operations logged with context
- **Secret management:** Environment variables for sensitive data

## 🚀 Deployment

### Docker Build
```bash
docker build -t document-ingestion .
```

### AWS Lambda
```bash
# Package for Lambda
zip -r function.zip src/ app.py requirements.txt

# Deploy with AWS CLI
aws lambda update-function-code \
  --function-name document-ingestion \
  --zip-file fileb://function.zip
```

## 🔧 Troubleshooting

### Common Issues

1. **"OpenAI API key not found"**
   - Check `OPENAI_API_KEY` environment variable

2. **"Supabase connection failed"**
   - Verify `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY`

3. **"Dependency injection failed"**
   - Run `python src/infrastructure/test_di.py` to diagnose

4. **"Lambda timeout"**
   - Reduce `MAX_ROWS` or increase Lambda timeout

## 📚 Further Reading

- [Clean Architecture by Robert Martin](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [dependency-injector Documentation](https://python-dependency-injector.ets-labs.org/)
- [Supabase pgvector Guide](https://supabase.com/docs/guides/database/extensions/pgvector)
- [OpenAI Embeddings API](https://platform.openai.com/docs/guides/embeddings)

## 🤝 Contributing

1. Follow clean architecture principles
2. Add tests for new features
3. Update documentation
4. Use dependency injection for new components
5. Maintain separation of concerns
