output "bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket
}

output "bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.this.arn
}

output "bucket_region" {
  description = "Region of the S3 bucket"
  value       = aws_s3_bucket.this.region
}

output "bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket_domain_name
}

output "bucket_regional_domain_name" {
  description = "Regional domain name of the S3 bucket"
  value       = aws_s3_bucket.this.bucket_regional_domain_name
}

output "lambda_integration_configured" {
  description = "Whether Lambda integration is configured"
  value       = var.lambda_integration != null
}

output "lifecycle_enabled" {
  description = "Whether lifecycle policies are enabled"
  value       = var.enable_lifecycle
}

# Backward compatibility
output "bucket" {
  description = "Bucket name (legacy output)"
  value       = aws_s3_bucket.this.bucket
}
