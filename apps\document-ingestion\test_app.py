"""
Simple test file for document ingestion Lambda function.
This ensures the CI/CD pipeline can run tests successfully.
"""

import sys
import os
import json
import pytest

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_import_app():
    """Test that the main app module can be imported."""
    try:
        import app

        assert app is not None
        print("✅ Successfully imported app module")
    except ImportError as e:
        pytest.fail(f"Failed to import app module: {e}")


def test_import_error_handler():
    """Test that the error handler module can be imported."""
    try:
        import error_handler

        assert error_handler is not None
        print("✅ Successfully imported error_handler module")
    except ImportError as e:
        pytest.fail(f"Failed to import error_handler module: {e}")


def test_import_logging_config():
    """Test that the logging config module can be imported."""
    try:
        import logging_config

        assert logging_config is not None
        print("✅ Successfully imported logging_config module")
    except ImportError as e:
        pytest.fail(f"Failed to import logging_config module: {e}")


def test_import_supabase_client():
    """Test that the supabase client module can be imported."""
    try:
        import supabase_client

        assert supabase_client is not None
        print("✅ Successfully imported supabase_client module")
    except ImportError as e:
        pytest.fail(f"Failed to import supabase_client module: {e}")


def test_requirements_file_exists():
    """Test that requirements.txt exists and is readable."""
    requirements_path = os.path.join(os.path.dirname(__file__), "requirements.txt")
    assert os.path.exists(requirements_path), "requirements.txt file not found"

    with open(requirements_path, "r") as f:
        content = f.read()
        assert len(content) > 0, "requirements.txt is empty"

    print("✅ requirements.txt file exists and is readable")


def test_dockerfile_exists():
    """Test that Dockerfile exists and is readable."""
    dockerfile_path = os.path.join(os.path.dirname(__file__), "Dockerfile")
    assert os.path.exists(dockerfile_path), "Dockerfile not found"

    with open(dockerfile_path, "r") as f:
        content = f.read()
        assert len(content) > 0, "Dockerfile is empty"

    print("✅ Dockerfile exists and is readable")


def test_api_gateway_handler():
    """Test that the Lambda can handle API Gateway requests."""
    try:
        import app

        # Mock API Gateway event
        api_event = {
            "path": "/document-ingestion",
            "httpMethod": "POST",
            "headers": {"Content-Type": "application/json"},
            "body": "{}",
        }

        # Mock Lambda context
        class MockContext:
            function_name = "test-function"
            function_version = "1"
            invoked_function_arn = "arn:aws:lambda:region:account:function:test"
            memory_limit_in_mb = 128
            remaining_time_in_millis = lambda: 1000
            aws_request_id = "test-request-id"

        context = MockContext()

        # Test API Gateway handler
        response = app.lambda_handler(api_event, context)

        assert response["statusCode"] == 200
        assert "body" in response
        assert "headers" in response

        body = json.loads(response["body"])
        assert body["status"] == "healthy"
        assert body["event_type"] == "api_gateway"

        print("✅ API Gateway handler works correctly")

    except ImportError as e:
        pytest.fail(f"Failed to import app module: {e}")
    except Exception as e:
        pytest.fail(f"API Gateway handler test failed: {e}")


def test_s3_event_handler():
    """Test that the Lambda can handle S3 events."""
    try:
        import app

        # Mock S3 event
        s3_event = {
            "Records": [
                {
                    "eventSource": "aws:s3",
                    "s3": {
                        "bucket": {"name": "test-bucket"},
                        "object": {"key": "test-user/uploaded/test.csv"},
                    },
                }
            ]
        }

        # Mock Lambda context
        class MockContext:
            function_name = "test-function"
            function_version = "1"
            invoked_function_arn = "arn:aws:lambda:region:account:function:test"
            memory_limit_in_mb = 128
            remaining_time_in_millis = lambda: 1000
            aws_request_id = "test-request-id"

        context = MockContext()

        # Test S3 event handler (will fail due to missing AWS credentials, but should not crash)
        try:
            app.lambda_handler(s3_event, context)
            # If we get here, the handler didn't crash
            print("✅ S3 event handler structure is correct")
        except Exception as e:
            # Expected to fail due to missing AWS credentials, but should be a specific error
            if "botocore" in str(e) or "AWS" in str(e):
                print(
                    "✅ S3 event handler structure is correct (failed as expected due to missing AWS credentials)"
                )
            else:
                pytest.fail(f"Unexpected error in S3 event handler: {e}")

    except ImportError as e:
        pytest.fail(f"Failed to import app module: {e}")


if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])
