"""
Comprehensive Logging Configuration for Document Ingestion Pipeline

This module provides structured logging, monitoring, and observability
for the document ingestion Lambda function with proper formatting
for CloudWatch and debugging.
"""

import json
import logging
import sys
import time
from datetime import datetime, timezone
from typing import Optional
import traceback
import os


class StructuredFormatter(logging.Formatter):
    """
    Custom formatter that outputs structured JSON logs for better parsing
    in CloudWatch and other log aggregation systems
    """

    def __init__(self):
        super().__init__()
        self.environment = os.getenv("ENVIRONMENT", "dev")
        self.service_name = "document-ingestion"
        self.version = os.getenv("SERVICE_VERSION", "1.0.0")

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON"""

        # Base log structure
        log_entry = {
            "timestamp": datetime.fromtimestamp(
                record.created, tz=timezone.utc
            ).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "service": self.service_name,
            "environment": self.environment,
            "version": self.version,
        }

        # Add AWS Lambda context if available
        if hasattr(record, "aws_request_id"):
            log_entry["aws_request_id"] = record.aws_request_id

        if hasattr(record, "function_name"):
            log_entry["function_name"] = record.function_name

        # Add custom fields from extra parameter
        if hasattr(record, "user_id"):
            log_entry["user_id"] = record.user_id

        if hasattr(record, "document_id"):
            log_entry["document_id"] = record.document_id

        if hasattr(record, "operation"):
            log_entry["operation"] = record.operation

        if hasattr(record, "duration_ms"):
            log_entry["duration_ms"] = record.duration_ms

        if hasattr(record, "error_code"):
            log_entry["error_code"] = record.error_code

        if hasattr(record, "metrics"):
            log_entry["metrics"] = record.metrics

        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info),
            }

        # Add stack trace for errors
        if record.levelno >= logging.ERROR and not record.exc_info:
            log_entry["stack_trace"] = traceback.format_stack()

        return json.dumps(log_entry, default=str, ensure_ascii=False)


class PerformanceLogger:
    """Context manager for logging operation performance"""

    def __init__(self, operation: str, logger: logging.Logger, **context):
        self.operation = operation
        self.logger = logger
        self.context = context
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        self.logger.info(
            f"Starting operation: {self.operation}",
            extra={"operation": self.operation, "status": "started", **self.context},
        )
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000

        if exc_type is None:
            self.logger.info(
                f"Completed operation: {self.operation}",
                extra={
                    "operation": self.operation,
                    "status": "completed",
                    "duration_ms": duration_ms,
                    **self.context,
                },
            )
        else:
            self.logger.error(
                f"Failed operation: {self.operation}",
                extra={
                    "operation": self.operation,
                    "status": "failed",
                    "duration_ms": duration_ms,
                    "error_type": exc_type.__name__ if exc_type else None,
                    **self.context,
                },
                exc_info=True,
            )


class DocumentIngestionLogger:
    """Specialized logger for document ingestion operations"""

    def __init__(self, name: str = "document_ingestion"):
        self.logger = logging.getLogger(name)
        self.setup_logging()

    def setup_logging(self):
        """Configure logging with structured formatter"""

        # Clear any existing handlers
        self.logger.handlers.clear()

        # Set log level based on environment
        log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.logger.setLevel(getattr(logging, log_level, logging.INFO))

        # Create console handler with structured formatter
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(StructuredFormatter())

        self.logger.addHandler(handler)

        # Prevent duplicate logs
        self.logger.propagate = False

    def log_document_processing_start(
        self, user_id: str, document_id: str, s3_key: str
    ):
        """Log start of document processing"""
        self.logger.info(
            "Document processing started",
            extra={
                "user_id": user_id,
                "document_id": document_id,
                "operation": "document_processing",
                "status": "started",
                "s3_key": s3_key,
            },
        )

    def log_document_processing_complete(
        self, user_id: str, document_id: str, processed_rows: int, duration_ms: float
    ):
        """Log successful completion of document processing"""
        self.logger.info(
            "Document processing completed successfully",
            extra={
                "user_id": user_id,
                "document_id": document_id,
                "operation": "document_processing",
                "status": "completed",
                "duration_ms": duration_ms,
                "metrics": {"processed_rows": processed_rows},
            },
        )

    def log_document_processing_failed(
        self, user_id: str, document_id: str, error: Exception, duration_ms: float
    ):
        """Log failed document processing"""
        self.logger.error(
            "Document processing failed",
            extra={
                "user_id": user_id,
                "document_id": document_id,
                "operation": "document_processing",
                "status": "failed",
                "duration_ms": duration_ms,
                "error_code": type(error).__name__,
            },
            exc_info=True,
        )

    def log_csv_parsing(
        self,
        user_id: str,
        document_id: str,
        rows: int,
        columns: int,
        file_size_bytes: int,
    ):
        """Log CSV parsing results"""
        self.logger.info(
            "CSV parsing completed",
            extra={
                "user_id": user_id,
                "document_id": document_id,
                "operation": "csv_parsing",
                "status": "completed",
                "metrics": {
                    "rows": rows,
                    "columns": columns,
                    "file_size_bytes": file_size_bytes,
                },
            },
        )

    def log_embedding_generation(
        self,
        user_id: str,
        document_id: str,
        batch_count: int,
        total_embeddings: int,
        duration_ms: float,
    ):
        """Log embedding generation results"""
        self.logger.info(
            "Embedding generation completed",
            extra={
                "user_id": user_id,
                "document_id": document_id,
                "operation": "embedding_generation",
                "status": "completed",
                "duration_ms": duration_ms,
                "metrics": {
                    "batch_count": batch_count,
                    "total_embeddings": total_embeddings,
                    "embeddings_per_second": total_embeddings / (duration_ms / 1000)
                    if duration_ms > 0
                    else 0,
                },
            },
        )

    def log_supabase_storage(
        self, user_id: str, document_id: str, embeddings_stored: int, duration_ms: float
    ):
        """Log Supabase storage results"""
        self.logger.info(
            "Supabase storage completed",
            extra={
                "user_id": user_id,
                "document_id": document_id,
                "operation": "supabase_storage",
                "status": "completed",
                "duration_ms": duration_ms,
                "metrics": {
                    "embeddings_stored": embeddings_stored,
                    "storage_rate_per_second": embeddings_stored / (duration_ms / 1000)
                    if duration_ms > 0
                    else 0,
                },
            },
        )

    def log_s3_operation(
        self,
        operation: str,
        bucket: str,
        key: str,
        success: bool,
        duration_ms: float,
        error: Optional[Exception] = None,
    ):
        """Log S3 operations"""
        level = logging.INFO if success else logging.ERROR
        message = f"S3 {operation} {'completed' if success else 'failed'}"

        extra = {
            "operation": f"s3_{operation}",
            "status": "completed" if success else "failed",
            "duration_ms": duration_ms,
            "metrics": {"bucket": bucket, "key": key},
        }

        if error:
            extra["error_code"] = type(error).__name__

        self.logger.log(level, message, extra=extra, exc_info=error if error else None)

    def log_api_call(
        self,
        api_name: str,
        operation: str,
        success: bool,
        duration_ms: float,
        error: Optional[Exception] = None,
        **metrics,
    ):
        """Log external API calls"""
        level = logging.INFO if success else logging.ERROR
        message = f"{api_name} {operation} {'completed' if success else 'failed'}"

        extra = {
            "operation": f"{api_name.lower()}_{operation}",
            "status": "completed" if success else "failed",
            "duration_ms": duration_ms,
            "metrics": metrics,
        }

        if error:
            extra["error_code"] = type(error).__name__

        self.logger.log(level, message, extra=extra, exc_info=error if error else None)

    def performance_context(self, operation: str, **context):
        """Create performance logging context manager"""
        return PerformanceLogger(operation, self.logger, **context)


def setup_lambda_logging(context=None):
    """Setup logging for Lambda environment with context"""

    # Create main logger
    doc_logger = DocumentIngestionLogger()

    # Add Lambda context information if available
    if context:
        # Add Lambda context to all log records
        old_factory = logging.getLogRecordFactory()

        def record_factory(*args, **kwargs):
            record = old_factory(*args, **kwargs)
            record.aws_request_id = getattr(context, "aws_request_id", None)
            record.function_name = getattr(context, "function_name", None)
            return record

        logging.setLogRecordFactory(record_factory)

    return doc_logger


# Global logger instance
doc_logger = DocumentIngestionLogger()
