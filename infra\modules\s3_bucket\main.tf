resource "aws_s3_bucket" "this" {
  bucket        = "${var.bucket_name}-${random_string.random.result}"
  force_destroy = var.force_destroy

  tags = merge(var.tags, {
    Name = var.bucket_name
  })
}

resource "aws_s3_bucket_versioning" "this" {
  bucket = aws_s3_bucket.this.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Enhanced CORS configuration for web uploads
resource "aws_s3_bucket_cors_configuration" "this" {
  bucket = aws_s3_bucket.this.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket_ownership_controls" "this" {
  bucket = aws_s3_bucket.this.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "this" {
  depends_on = [aws_s3_bucket_ownership_controls.this]

  bucket = aws_s3_bucket.this.id
  acl    = "private"
}

# Lambda Integration - S3 bucket notification
resource "aws_s3_bucket_notification" "lambda_notification" {
  count = var.lambda_integration != null ? 1 : 0

  bucket = aws_s3_bucket.this.id

  lambda_function {
    lambda_function_arn = var.lambda_integration.lambda_function_arn
    events              = var.lambda_integration.events
    filter_prefix       = var.lambda_integration.filter_prefix
    filter_suffix       = var.lambda_integration.filter_suffix
  }

  depends_on = [aws_lambda_permission.allow_bucket]
}

# Grant Lambda permission to be invoked by S3
resource "aws_lambda_permission" "allow_bucket" {
  count = var.lambda_integration != null ? 1 : 0

  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = var.lambda_integration.lambda_function_name
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.this.arn
}

# Lifecycle configuration for cost optimization
resource "aws_s3_bucket_lifecycle_configuration" "this" {
  count = var.enable_lifecycle ? 1 : 0

  bucket = aws_s3_bucket.this.id

  dynamic "rule" {
    for_each = var.lifecycle_rules
    content {
      id     = rule.value.id
      status = rule.value.status

      dynamic "transition" {
        for_each = rule.value.transition
        content {
          days          = transition.value.days
          storage_class = transition.value.storage_class
        }
      }

      dynamic "expiration" {
        for_each = rule.value.expiration != null ? [rule.value.expiration] : []
        content {
          days = expiration.value.days
        }
      }

      dynamic "abort_incomplete_multipart_upload" {
        for_each = rule.value.abort_incomplete_multipart_upload != null ? [rule.value.abort_incomplete_multipart_upload] : []
        content {
          days_after_initiation = abort_incomplete_multipart_upload.value.days_after_initiation
        }
      }
    }
  }
}

# Enhanced bucket policy with Lambda access
resource "aws_s3_bucket_policy" "this" {
  count = var.cloudfront_origin_access_identity != null || var.lambda_integration != null ? 1 : 0

  bucket = aws_s3_bucket.this.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = concat(
      # CloudFront policy (if configured)
      var.cloudfront_origin_access_identity != null ? [
        {
          Sid    = "AllowCloudFrontOriginAccess"
          Effect = "Allow"
          Principal = {
            AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${var.cloudfront_origin_access_identity}"
          }
          Action   = "s3:GetObject"
          Resource = "${aws_s3_bucket.this.arn}/*"
        }
      ] : [],
      # Lambda access policy (if configured)
      var.lambda_integration != null ? [
        {
          Sid    = "EnforceSSLOnly"
          Effect = "Deny"
          Principal = {
            AWS = "*"
          }
          Action = "s3:*"
          Resource = [
            aws_s3_bucket.this.arn,
            "${aws_s3_bucket.this.arn}/*"
          ]
          Condition = {
            Bool = {
              "aws:SecureTransport" = "false"
            }
          }
        },
        {
          Sid    = "AllowLambdaAccess"
          Effect = "Allow"
          Principal = {
            AWS = var.lambda_integration.lambda_execution_role_arn
          }
          Action = [
            "s3:GetObject",
            "s3:PutObject",
            "s3:DeleteObject",
            "s3:ListBucket"
          ]
          Resource = [
            aws_s3_bucket.this.arn,
            "${aws_s3_bucket.this.arn}/*"
          ]
        }
      ] : []
    )
  })
}
