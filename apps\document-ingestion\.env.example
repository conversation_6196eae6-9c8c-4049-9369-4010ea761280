# Environment Configuration for Document Ingestion Pipeline
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Supabase Configuration
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# =============================================================================
# OPTIONAL CONFIGURATION (with defaults)
# =============================================================================

# Environment Settings
ENVIRONMENT=dev
SERVICE_VERSION=1.0.0
LOG_LEVEL=INFO

# OpenAI Embedding Configuration
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=512

# Processing Limits
MAX_FILE_SIZE_BYTES=52428800
MAX_ROWS=100000
BATCH_SIZE=100

# Retry Configuration
RETRY_ATTEMPTS=3
RETRY_MIN_WAIT=1
RETRY_MAX_WAIT=60

# Batch Processing Configuration
EMBEDDING_BATCH_SIZE=100
SUPABASE_BATCH_SIZE=100

# Vector Search Configuration
SIMILARITY_THRESHOLD=0.7
MAX_SEARCH_RESULTS=10

# =============================================================================
# CONFIGURATION DESCRIPTIONS
# =============================================================================

# OPENAI_API_KEY
# Description: Your OpenAI API key for generating embeddings
# Required: Yes
# Example: sk-proj-abc123...

# OPENAI_EMBEDDING_MODEL
# Description: OpenAI embedding model to use
# Required: No
# Default: text-embedding-3-small
# Options: text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002

# OPENAI_EMBEDDING_DIMENSIONS
# Description: Number of dimensions for embeddings (only for text-embedding-3-* models)
# Required: No
# Default: 512
# Range: 256-3072 (for text-embedding-3-small), 256-3072 (for text-embedding-3-large)

# SUPABASE_URL
# Description: Your Supabase project URL
# Required: Yes
# Format: https://your-project-id.supabase.co

# SUPABASE_SERVICE_ROLE_KEY
# Description: Supabase service role key (bypasses RLS)
# Required: Yes
# Note: Use service role key for Lambda functions to bypass Row Level Security

# ENVIRONMENT
# Description: Deployment environment
# Required: No
# Default: dev
# Options: dev, development, local, staging, prod, production

# SERVICE_VERSION
# Description: Version of the service for logging and monitoring
# Required: No
# Default: 1.0.0

# LOG_LEVEL
# Description: Logging level
# Required: No
# Default: INFO
# Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

# MAX_FILE_SIZE_BYTES
# Description: Maximum allowed file size in bytes
# Required: No
# Default: 52428800 (50MB)
# Note: AWS Lambda has payload limits, adjust accordingly

# MAX_ROWS
# Description: Maximum number of rows allowed in CSV files
# Required: No
# Default: 100000
# Note: Higher values may cause Lambda timeouts

# BATCH_SIZE
# Description: General batch size for processing operations
# Required: No
# Default: 100

# RETRY_ATTEMPTS
# Description: Number of retry attempts for failed operations
# Required: No
# Default: 3

# RETRY_MIN_WAIT
# Description: Minimum wait time between retries (seconds)
# Required: No
# Default: 1

# RETRY_MAX_WAIT
# Description: Maximum wait time between retries (seconds)
# Required: No
# Default: 60

# EMBEDDING_BATCH_SIZE
# Description: Batch size for OpenAI embedding API calls
# Required: No
# Default: 100
# Note: OpenAI allows up to 2048 inputs per request

# SUPABASE_BATCH_SIZE
# Description: Batch size for Supabase insert operations
# Required: No
# Default: 100
# Note: Larger batches may hit payload limits

# SIMILARITY_THRESHOLD
# Description: Default similarity threshold for vector searches
# Required: No
# Default: 0.7
# Range: 0.0-1.0

# MAX_SEARCH_RESULTS
# Description: Maximum number of results to return from vector searches
# Required: No
# Default: 10

# =============================================================================
# DEPLOYMENT SPECIFIC CONFIGURATIONS
# =============================================================================

# Development Environment
# ENVIRONMENT=dev
# LOG_LEVEL=DEBUG
# MAX_ROWS=1000

# Staging Environment
# ENVIRONMENT=staging
# LOG_LEVEL=INFO
# MAX_ROWS=10000

# Production Environment
# ENVIRONMENT=production
# LOG_LEVEL=WARNING
# MAX_ROWS=100000

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit actual API keys or secrets to version control
# 2. Use AWS Secrets Manager or Parameter Store for production deployments
# 3. Rotate API keys regularly
# 4. Use least privilege principle for Supabase keys
# 5. Monitor API usage and set up billing alerts

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common Issues:
# 1. "OpenAI API key not found" - Check OPENAI_API_KEY is set correctly
# 2. "Supabase connection failed" - Verify SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY
# 3. "File too large" - Adjust MAX_FILE_SIZE_BYTES or split large files
# 4. "Lambda timeout" - Reduce MAX_ROWS or increase Lambda timeout
# 5. "Rate limit exceeded" - Reduce EMBEDDING_BATCH_SIZE or add delays
