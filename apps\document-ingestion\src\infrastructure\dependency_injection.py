"""
Dependency Injection Container for Document Ingestion Pipeline

This module provides a dependency injection container that wires up all the
dependencies according to clean architecture principles.
"""

from typing import Optional

from ..domain.interfaces import (
    IDocumentRepository, IProductEmbeddingRepository, IFileStorage,
    IEmbeddingService, ICSVProcessor, IEventPublisher, ILogger,
    IMetricsCollector, IConfiguration
)
from ..domain.services import (
    SearchableTextGenerator, EmbeddingGenerationService,
    DocumentValidationService, MultiTenantSecurityService
)
from ..application.use_cases import ProcessDocumentUseCase
from .configuration import EnvironmentConfiguration
from .logging import DomainLogger, setup_lambda_logging
from .metrics import create_metrics_collector


class DependencyContainer:
    """Dependency injection container for the application"""
    
    def __init__(self):
        self._instances = {}
        self._configuration = None
        self._logger = None
        self._metrics_collector = None
    
    def get_configuration(self) -> IConfiguration:
        """Get configuration instance"""
        if self._configuration is None:
            self._configuration = EnvironmentConfiguration()
        return self._configuration
    
    def get_logger(self) -> ILogger:
        """Get logger instance"""
        if self._logger is None:
            config = self.get_configuration()
            self._logger = DomainLogger(config)
        return self._logger
    
    def get_metrics_collector(self) -> IMetricsCollector:
        """Get metrics collector instance"""
        if self._metrics_collector is None:
            logger = self.get_logger()
            config = self.get_configuration()
            use_cloudwatch = config.is_production()
            self._metrics_collector = create_metrics_collector(logger, use_cloudwatch)
        return self._metrics_collector
    
    def get_document_repository(self) -> IDocumentRepository:
        """Get document repository instance"""
        if 'document_repository' not in self._instances:
            from .repositories import SupabaseDocumentRepository
            config = self.get_configuration()
            logger = self.get_logger()
            self._instances['document_repository'] = SupabaseDocumentRepository(config, logger)
        return self._instances['document_repository']
    
    def get_product_embedding_repository(self) -> IProductEmbeddingRepository:
        """Get product embedding repository instance"""
        if 'embedding_repository' not in self._instances:
            from .repositories import SupabaseProductEmbeddingRepository
            config = self.get_configuration()
            logger = self.get_logger()
            self._instances['embedding_repository'] = SupabaseProductEmbeddingRepository(config, logger)
        return self._instances['embedding_repository']
    
    def get_file_storage(self) -> IFileStorage:
        """Get file storage instance"""
        if 'file_storage' not in self._instances:
            from .storage import S3FileStorage
            logger = self.get_logger()
            metrics = self.get_metrics_collector()
            self._instances['file_storage'] = S3FileStorage(logger, metrics)
        return self._instances['file_storage']
    
    def get_embedding_service(self) -> IEmbeddingService:
        """Get embedding service instance"""
        if 'embedding_service' not in self._instances:
            from .services import OpenAIEmbeddingService
            config = self.get_configuration()
            logger = self.get_logger()
            metrics = self.get_metrics_collector()
            self._instances['embedding_service'] = OpenAIEmbeddingService(config, logger, metrics)
        return self._instances['embedding_service']
    
    def get_csv_processor(self) -> ICSVProcessor:
        """Get CSV processor instance"""
        if 'csv_processor' not in self._instances:
            from .processors import PandasCSVProcessor
            config = self.get_configuration()
            logger = self.get_logger()
            self._instances['csv_processor'] = PandasCSVProcessor(config, logger)
        return self._instances['csv_processor']
    
    def get_event_publisher(self) -> IEventPublisher:
        """Get event publisher instance"""
        if 'event_publisher' not in self._instances:
            from .events import LoggingEventPublisher
            logger = self.get_logger()
            self._instances['event_publisher'] = LoggingEventPublisher(logger)
        return self._instances['event_publisher']
    
    def get_searchable_text_generator(self) -> SearchableTextGenerator:
        """Get searchable text generator instance"""
        if 'text_generator' not in self._instances:
            self._instances['text_generator'] = SearchableTextGenerator()
        return self._instances['text_generator']
    
    def get_embedding_generation_service(self) -> EmbeddingGenerationService:
        """Get embedding generation service instance"""
        if 'embedding_generation_service' not in self._instances:
            embedding_service = self.get_embedding_service()
            text_generator = self.get_searchable_text_generator()
            logger = self.get_logger()
            self._instances['embedding_generation_service'] = EmbeddingGenerationService(
                embedding_service, text_generator, logger
            )
        return self._instances['embedding_generation_service']
    
    def get_document_validation_service(self) -> DocumentValidationService:
        """Get document validation service instance"""
        if 'validation_service' not in self._instances:
            logger = self.get_logger()
            self._instances['validation_service'] = DocumentValidationService(logger)
        return self._instances['validation_service']
    
    def get_security_service(self) -> MultiTenantSecurityService:
        """Get multi-tenant security service instance"""
        if 'security_service' not in self._instances:
            logger = self.get_logger()
            self._instances['security_service'] = MultiTenantSecurityService(logger)
        return self._instances['security_service']
    
    def get_process_document_use_case(self) -> ProcessDocumentUseCase:
        """Get process document use case instance"""
        if 'process_document_use_case' not in self._instances:
            self._instances['process_document_use_case'] = ProcessDocumentUseCase(
                document_repo=self.get_document_repository(),
                embedding_repo=self.get_product_embedding_repository(),
                file_storage=self.get_file_storage(),
                csv_processor=self.get_csv_processor(),
                embedding_service=self.get_embedding_generation_service(),
                validation_service=self.get_document_validation_service(),
                security_service=self.get_security_service(),
                event_publisher=self.get_event_publisher(),
                logger=self.get_logger(),
                metrics=self.get_metrics_collector(),
                config=self.get_configuration()
            )
        return self._instances['process_document_use_case']
    
    def setup_lambda_logging(self, context=None) -> ILogger:
        """Setup logging for Lambda environment"""
        config = self.get_configuration()
        self._logger = setup_lambda_logging(config, context)
        return self._logger
    
    def reset(self):
        """Reset all instances (useful for testing)"""
        self._instances.clear()
        self._configuration = None
        self._logger = None
        self._metrics_collector = None
