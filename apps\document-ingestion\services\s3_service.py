"""
S3 Service for Document Ingestion Pipeline

Handles all S3 operations including file movement, validation, and error handling.
"""

import os
import logging
import re
from typing import Dict, Any, Optional, Tuple
from urllib.parse import unquote_plus

import boto3
from botocore.exceptions import ClientError

from error_handler import S3AccessError, log_error_details, error_metrics

logger = logging.getLogger(__name__)


class S3Service:
    """Service for handling S3 operations in the document ingestion pipeline"""
    
    def __init__(self):
        self.client = boto3.client("s3")
    
    def extract_s3_info(self, record: Dict[str, Any]) -> Tuple[str, str]:
        """
        Extract bucket and key from S3 event record
        
        Args:
            record: S3 event record
            
        Returns:
            Tuple of (bucket, key)
        """
        bucket = record["s3"]["bucket"]["name"]
        key = unquote_plus(record["s3"]["object"]["key"])
        return bucket, key
    
    def extract_user_info_from_s3_key(self, s3_key: str) -> <PERSON><PERSON>[Optional[str], Optional[Dict[str, str]]]:
        """
        Extract user ID and file information from S3 key
        
        Expected format: {user_id}/uploaded/{filename}.csv
        
        Args:
            s3_key: S3 object key
            
        Returns:
            Tuple of (user_id, file_info) or (None, None) if invalid
        """
        try:
            # Pattern: {user_id}/uploaded/{filename}.csv
            pattern = r"^([^/]+)/uploaded/(.+\.csv)$"
            match = re.match(pattern, s3_key)
            
            if match:
                user_id = match.group(1)
                filename = match.group(2)
                return user_id, {"filename": filename, "folder": "uploaded"}
            
            logger.warning(f"S3 key does not match expected pattern: {s3_key}")
            return None, None
            
        except Exception as e:
            logger.error(f"Error extracting user info from S3 key {s3_key}: {e}")
            return None, None
    
    def validate_file_format(self, key: str) -> bool:
        """
        Validate that the file is a CSV
        
        Args:
            key: S3 object key
            
        Returns:
            True if valid CSV file
        """
        return key.lower().endswith(".csv")
    
    def download_csv_content(self, bucket: str, key: str) -> bytes:
        """
        Download CSV file content from S3
        
        Args:
            bucket: S3 bucket name
            key: S3 object key
            
        Returns:
            CSV file content as bytes
        """
        try:
            logger.info(f"Downloading CSV file: s3://{bucket}/{key}")
            response = self.client.get_object(Bucket=bucket, Key=key)
            return response["Body"].read()
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_metrics.record_error(f"s3_download_error_{error_code}")
            log_error_details(e, {"operation": "download_csv", "bucket": bucket, "key": key})
            raise S3AccessError(f"Failed to download CSV file: {e}")
    
    def move_file(self, bucket: str, source_key: str, user_id: str, target_folder: str) -> bool:
        """
        Move S3 file between folders with comprehensive error handling
        
        Args:
            bucket: S3 bucket name
            source_key: Source S3 key
            user_id: User identifier
            target_folder: Target folder (processed/failed)
            
        Returns:
            True if successful
        """
        try:
            # Extract filename from source key
            filename = os.path.basename(source_key)
            
            # Construct target key
            target_key = f"{user_id}/{target_folder}/{filename}"
            
            logger.info(f"Moving S3 file from {source_key} to {target_key}")
            
            # Check if source file exists
            try:
                self.client.head_object(Bucket=bucket, Key=source_key)
            except ClientError as e:
                if e.response["Error"]["Code"] == "404":
                    logger.warning(f"Source file not found: {source_key}")
                    return False
                else:
                    raise
            
            # Copy object to new location
            copy_source = {"Bucket": bucket, "Key": source_key}
            self.client.copy_object(CopySource=copy_source, Bucket=bucket, Key=target_key)
            
            # Verify copy was successful
            try:
                self.client.head_object(Bucket=bucket, Key=target_key)
            except ClientError as e:
                error_metrics.record_error("s3_copy_verification_failed")
                raise S3AccessError(f"Failed to verify copied file: {e}")
            
            # Delete original object
            self.client.delete_object(Bucket=bucket, Key=source_key)
            
            # Verify deletion
            try:
                self.client.head_object(Bucket=bucket, Key=source_key)
                # If we get here, the file still exists
                logger.warning(f"Original file still exists after deletion: {source_key}")
            except ClientError as e:
                if e.response["Error"]["Code"] == "404":
                    # File successfully deleted
                    pass
                else:
                    logger.warning(f"Error verifying file deletion: {e}")
            
            logger.info(f"Successfully moved file from {source_key} to {target_key}")
            error_metrics.record_success()
            return True
            
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_metrics.record_error(f"s3_error_{error_code}")
            log_error_details(
                e,
                {
                    "operation": "move_s3_file",
                    "source_key": source_key,
                    "target_folder": target_folder,
                    "user_id": user_id,
                },
            )
            logger.error(f"Error moving S3 file {source_key}: {e}")
            return False
        except Exception as e:
            error_metrics.record_error("s3_move_unexpected_error")
            log_error_details(
                e,
                {
                    "operation": "move_s3_file",
                    "source_key": source_key,
                    "target_folder": target_folder,
                    "user_id": user_id,
                },
            )
            logger.error(f"Unexpected error moving S3 file {source_key}: {e}")
            return False 