variable "bucket_name" {
  description = "The name of the bucket."
}

variable "cloudfront_origin_access_identity" {
  default     = null
  description = "CloudFront Origin Access Identity for the bucket."
}

variable "website_configuration" {
  description = "Website configuration"
  type = object({
    index_document_suffix = optional(string, "index.html")
    error_document_key    = optional(string, "error.html")
  })
  default = null
}

variable "force_destroy" {
  description = "Object bucket will be force to destroy"
  default     = false
}

# Lambda Integration Variables
variable "lambda_integration" {
  description = "Lambda integration configuration"
  type = object({
    lambda_function_arn = string
    lambda_function_name = string
    lambda_execution_role_arn = string
    events = optional(list(string), ["s3:ObjectCreated:*"])
    filter_prefix = optional(string, "")
    filter_suffix = optional(string, "")
  })
  default = null
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "enable_lifecycle" {
  description = "Enable S3 lifecycle policies for cost optimization"
  type        = bool
  default     = false
}

variable "lifecycle_rules" {
  description = "S3 lifecycle rules configuration"
  type = list(object({
    id = string
    status = string
    transition = optional(list(object({
      days = number
      storage_class = string
    })), [])
    expiration = optional(object({
      days = number
    }), null)
    abort_incomplete_multipart_upload = optional(object({
      days_after_initiation = number
    }), null)
  }))
  default = []
}
