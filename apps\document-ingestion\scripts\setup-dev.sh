#!/bin/bash

# Development Environment Setup Script
# Document Ingestion Pipeline - Clean Architecture

set -e  # Exit on any error

echo "🚀 Setting up Document Ingestion Pipeline Development Environment"
echo "================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.12 is available
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3.12 &> /dev/null; then
        PYTHON_CMD="python3.12"
    elif command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [[ "$PYTHON_VERSION" == "3.12" ]]; then
            PYTHON_CMD="python3"
        else
            print_error "Python 3.12 is required. Found Python $PYTHON_VERSION"
            exit 1
        fi
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [[ "$PYTHON_VERSION" == "3.12" ]]; then
            PYTHON_CMD="python"
        else
            print_error "Python 3.12 is required. Found Python $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3.12 is not installed. Please install Python 3.12 first."
        exit 1
    fi
    
    print_success "Python 3.12 found: $PYTHON_CMD"
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment..."
    
    if [ -d ".venv" ]; then
        print_warning "Virtual environment already exists. Removing old one..."
        rm -rf .venv
    fi
    
    $PYTHON_CMD -m venv .venv
    print_success "Virtual environment created"
}

# Activate virtual environment
activate_venv() {
    print_status "Activating virtual environment..."
    
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        source .venv/Scripts/activate
    else
        source .venv/bin/activate
    fi
    
    print_success "Virtual environment activated"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Upgrade pip
    python -m pip install --upgrade pip
    
    # Install development dependencies
    pip install -e ".[dev]"
    
    print_success "Dependencies installed"
}

# Setup environment file
setup_env_file() {
    print_status "Setting up environment file..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please edit .env file with your actual API keys and configuration"
    else
        print_warning ".env file already exists. Skipping..."
    fi
}

# Install pre-commit hooks
setup_pre_commit() {
    print_status "Setting up pre-commit hooks..."
    
    pre-commit install
    print_success "Pre-commit hooks installed"
}

# Run initial tests
run_tests() {
    print_status "Running initial tests..."
    
    # Run unit tests only (fast)
    pytest tests/unit -v -m "unit"
    
    print_success "Initial tests passed"
}

# Test dependency injection
test_di() {
    print_status "Testing dependency injection..."
    
    python -c "
from src.infrastructure.dependency_injection import ApplicationContainer
container = ApplicationContainer()
config = container.config()
logger = container.logger()
print('✅ Dependency injection working correctly!')
"
    
    print_success "Dependency injection test passed"
}

# Setup IDE configuration
setup_ide() {
    print_status "Setting up IDE configuration..."
    
    # Create .vscode directory if it doesn't exist
    mkdir -p .vscode
    
    # Create VS Code settings
    cat > .vscode/settings.json << EOF
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=100"],
    "python.sortImports.args": ["--profile=black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        ".mypy_cache": true,
        ".coverage": true,
        "htmlcov": true
    }
}
EOF
    
    print_success "VS Code configuration created"
}

# Main setup function
main() {
    echo ""
    print_status "Starting development environment setup..."
    echo ""
    
    # Run setup steps
    check_python
    create_venv
    activate_venv
    install_dependencies
    setup_env_file
    setup_pre_commit
    test_di
    run_tests
    setup_ide
    
    echo ""
    print_success "🎉 Development environment setup complete!"
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your actual API keys"
    echo "2. Activate virtual environment: source .venv/bin/activate (Linux/Mac) or .venv\\Scripts\\activate (Windows)"
    echo "3. Run tests: make test"
    echo "4. Start coding! 🚀"
    echo ""
    echo "Available commands:"
    echo "  make help          - Show all available commands"
    echo "  make test          - Run all tests"
    echo "  make test-unit     - Run unit tests only"
    echo "  make lint          - Run code quality checks"
    echo "  make format        - Format code"
    echo ""
}

# Run main function
main "$@"
