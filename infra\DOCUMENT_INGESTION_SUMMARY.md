# Document Ingestion Pipeline - Terraform Infrastructure Summary

## Overview

This document summarizes the Terraform infrastructure setup for the document ingestion pipeline with S3 integration, following the established patterns in the EzyChat infrastructure.

## What Was Added

### 1. Lambda Service Configuration
**File**: `infra/iac/core/configs/lambda_services.yaml`

Added the `document-ingestion-lambda` service with:
- Container image configuration
- Environment variables for OpenAI and Supabase
- Extended timeout (15 minutes) for large file processing
- Increased memory (1GB) for embedding generation
- Custom IAM policy for S3 and Secrets Manager access

### 2. Document Storage Module
**Directory**: `infra/modules/document_storage/`

New S3 bucket module with:
- **S3 Bucket**: Private bucket with versioning and lifecycle policies
- **Lambda Integration**: Automatic S3 event notifications
- **Security**: SSL-only access, bucket policies, CORS configuration
- **Cost Optimization**: Automatic storage class transitions
- **Multi-tenant Support**: User-specific folder structure

### 3. Core Infrastructure Integration
**File**: `infra/iac/core/document_storage.tf`

Integrated document storage with existing lambda platform:
- References lambda function outputs
- Consistent tagging and naming
- Proper module dependencies

### 4. Variable Management
**File**: `infra/iac/core/vars.tf`

Added variables for secret management:
- `openai_api_key_secret_arn`
- `supabase_url_secret_arn`
- `supabase_key_secret_arn`

### 5. Template Processing
**File**: `infra/iac/core/lambda_app_platform.tf`

Enhanced lambda platform to handle:
- Template variable substitution in YAML configuration
- Dynamic environment variable processing
- IAM policy template replacement

## Architecture Pattern

The solution follows the established EzyChat infrastructure patterns:

```
┌─────────────────────────────────────────────────────────┐
│                EzyChat Infrastructure                   │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │ Lambda Platform │  │ Document Storage│              │
│  │   (Existing)    │  │   (New Module)  │              │
│  └─────────────────┘  └─────────────────┘              │
│           │                      │                      │
│  ┌─────────────────┐  ┌─────────────────┐              │
│  │ YAML Config     │  │ S3 Event        │              │
│  │ (Enhanced)      │  │ Notifications   │              │
│  └─────────────────┘  └─────────────────┘              │
└─────────────────────────────────────────────────────────┘
```

## Key Features

### 1. YAML-Driven Configuration
- Single configuration file for all lambda services
- Template variable support for environment-specific values
- Consistent with existing ECS and Lambda patterns

### 2. Modular Design
- Reusable document storage module
- Follows existing module patterns
- Clear separation of concerns

### 3. Security First
- SSL-only S3 access
- IAM least privilege principles
- AWS Secrets Manager integration
- Private bucket with controlled access

### 4. Cost Optimization
- S3 lifecycle policies (IA → Glacier → Deletion)
- Lambda timeout and memory optimization
- CloudWatch log retention limits

### 5. Multi-tenant Support
- User-specific S3 folder structure
- Isolated processing per user
- Scalable architecture

## S3 Folder Structure

```
s3://{bucket-name}/
├── {user_id}/
│   ├── uploaded/          # Files uploaded by users
│   │   ├── products.csv
│   │   └── inventory.csv
│   ├── processed/         # Successfully processed files
│   └── failed/           # Files that failed processing
```

## Integration Points

### 1. Lambda Function
- **Trigger**: S3 ObjectCreated events on `*/uploaded/*.csv`
- **Processing**: CSV parsing, embedding generation, Supabase storage
- **Output**: Files moved to `processed/` or `failed/` folders

### 2. Environment Variables
- **OpenAI API Key**: For embedding generation
- **Supabase URL/Key**: For data storage
- **Logging**: Structured logging with CloudWatch

### 3. IAM Permissions
- **S3 Access**: Read/write/delete on document bucket
- **Secrets Manager**: Read access to API keys
- **CloudWatch**: Logging permissions

## Deployment Process

### 1. Prerequisites
```bash
# Create AWS Secrets
aws secretsmanager create-secret --name "ezychat/openai-api-key" --secret-string "your-key"
aws secretsmanager create-secret --name "ezychat/supabase-url" --secret-string "https://your-project.supabase.co"
aws secretsmanager create-secret --name "ezychat/supabase-key" --secret-string "your-service-key"

# Create ECR repository
aws ecr create-repository --repository-name lambda-document-ingestion
```

### 2. Build and Deploy
```bash
# Build Lambda container
cd apps/document-ingestion
docker build -t lambda-document-ingestion .
docker push 381491882604.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-document-ingestion:latest

# Deploy infrastructure
cd infra/iac/aws-ezychat-uat-tf
terraform init
terraform plan
terraform apply
```

### 3. Test Upload
```bash
# Upload test file
aws s3 cp test.csv s3://uat-ezychat-documents-xxxxx/user123/uploaded/test.csv

# Monitor processing
aws logs tail /aws/lambda/uat-lambda-api-document-ingestion-lambda --follow
```

## Configuration Management

### Environment-Specific Settings
- **UAT**: Lower timeout, smaller memory, test secrets
- **Production**: Higher limits, production secrets, monitoring

### Template Variables
- `{{OPENAI_API_KEY_ARN}}`: Replaced with actual secret ARN
- `{{SUPABASE_URL_ARN}}`: Replaced with actual secret ARN
- `{{SUPABASE_KEY_ARN}}`: Replaced with actual secret ARN
- `{{DOCUMENT_BUCKET_NAME}}`: Replaced with actual bucket name

## Monitoring and Observability

### 1. CloudWatch Metrics
- Lambda duration, errors, throttles
- S3 request counts and errors
- API Gateway performance

### 2. Logging
- Structured JSON logging
- Request/response correlation
- Error tracking and alerting

### 3. Health Checks
- Lambda function health endpoints
- S3 bucket accessibility
- Secret availability

## Security Considerations

### 1. Data Protection
- All data encrypted in transit (HTTPS)
- S3 bucket versioning for data recovery
- Private bucket with controlled access

### 2. Access Control
- IAM roles with minimal required permissions
- S3 bucket policies for additional security
- Secrets stored in AWS Secrets Manager

### 3. Compliance
- Audit trails via CloudTrail
- Resource tagging for cost allocation
- Log retention policies

## Cost Optimization

### 1. S3 Storage
- Lifecycle policies for automatic transitions
- Intelligent Tiering for cost savings
- Cleanup of incomplete uploads

### 2. Lambda Execution
- Optimized timeout and memory settings
- Batch processing for efficiency
- CloudWatch log retention limits

### 3. Monitoring
- Cost allocation tags
- Usage monitoring and alerts
- Resource right-sizing recommendations

## Future Enhancements

### 1. Scalability
- S3 Transfer Acceleration for global uploads
- Lambda provisioned concurrency for performance
- Multi-region deployment options

### 2. Features
- Support for additional file formats (PDF, DOCX)
- Real-time processing notifications
- Advanced error handling and retry logic

### 3. Monitoring
- Custom CloudWatch dashboards
- SNS notifications for processing status
- Performance optimization recommendations

## Troubleshooting Guide

### Common Issues
1. **Lambda Timeout**: Increase timeout or split large files
2. **Memory Issues**: Increase memory allocation
3. **S3 Permissions**: Verify IAM policy configuration
4. **Secret Access**: Check Secrets Manager ARNs and permissions

### Debugging Steps
1. Check CloudWatch logs for error details
2. Verify S3 bucket and file permissions
3. Test with small files first
4. Monitor AWS service quotas and limits

## Conclusion

The document ingestion pipeline infrastructure follows the established EzyChat patterns while adding new capabilities for S3 integration and document processing. The solution is:

- **Scalable**: Supports multiple users and large files
- **Secure**: Implements security best practices
- **Cost-effective**: Optimized for AWS costs
- **Maintainable**: Uses established patterns and modular design
- **Observable**: Comprehensive monitoring and logging

The infrastructure is ready for deployment and can be extended for additional document processing requirements. 