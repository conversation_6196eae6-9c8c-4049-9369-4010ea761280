# Document Storage Platform for ${local.api_domain_name}
# This creates S3 bucket with Lambda integration for document ingestion pipeline

# Create document storage bucket with Lambda integration
module "document_storage" {
  source = "../../modules/s3_bucket"

  bucket_name = "${var.env}-ezychat-documents"
  force_destroy = false

  # Lambda integration for document ingestion
  lambda_integration = {
    lambda_function_arn = module.lambda_platform.lambda_function_arns["document-ingestion-lambda"]
    lambda_function_name = module.lambda_platform.lambda_function_names["document-ingestion-lambda"]
    lambda_execution_role_arn = module.lambda_platform.lambda_execution_role_arns["document-ingestion-lambda"]
    events = ["s3:ObjectCreated:*"]
    filter_prefix = "*/uploaded/"
    filter_suffix = ".csv"
  }

  # Enable lifecycle policies for cost optimization
  enable_lifecycle = false
  
  tags = merge(local.this.tags, {
    Name     = "${local.api_domain_name}-document-storage"
    Platform = "Document Storage"
    Domain   = local.api_domain_name
  })
}

# Output Document Storage information
output "document_storage" {
  description = "Document Storage outputs"
  value = {
    bucket_name                = module.document_storage.bucket_name
    bucket_arn                 = module.document_storage.bucket_arn
    bucket_region              = module.document_storage.bucket_region
    bucket_domain_name         = module.document_storage.bucket_domain_name
    bucket_regional_domain_name = module.document_storage.bucket_regional_domain_name
    lambda_integration_configured = module.document_storage.lambda_integration_configured
    lifecycle_enabled = module.document_storage.lifecycle_enabled
  }
} 