name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/document-ingestion/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/document-ingestion/**'

env:
  PYTHON_VERSION: '3.12'
  WORKING_DIRECTORY: apps/document-ingestion

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.12']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    
    - name: Install dependencies
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,test]"
    
    - name: Set up test environment
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        cp .env.example .env
        # Set test environment variables
        echo "OPENAI_API_KEY=test-key" >> .env
        echo "SUPABASE_URL=https://test.supabase.co" >> .env
        echo "SUPABASE_SERVICE_ROLE_KEY=test-key" >> .env
    
    - name: Run unit tests
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        pytest tests/unit -v --cov=src --cov-report=xml --cov-report=term-missing
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ${{ env.WORKING_DIRECTORY }}/coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  lint:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[lint]"
    
    - name: Run flake8
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        flake8 src tests --output-file=flake8-report.txt
    
    - name: Run mypy
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        mypy src --junit-xml=mypy-report.xml
    
    - name: Run bandit security check
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        bandit -r src -f json -o bandit-report.json
    
    - name: Upload lint reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: lint-reports
        path: |
          ${{ env.WORKING_DIRECTORY }}/flake8-report.txt
          ${{ env.WORKING_DIRECTORY }}/mypy-report.xml
          ${{ env.WORKING_DIRECTORY }}/bandit-report.json

  format-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install formatting tools
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        python -m pip install --upgrade pip
        pip install black isort
    
    - name: Check code formatting with black
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        black --check src tests
    
    - name: Check import sorting with isort
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        isort --check-only src tests

  dependency-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        python -m pip install --upgrade pip
        pip install safety
        pip install -r requirements.txt
    
    - name: Run safety check
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        safety check --json --output safety-report.json
      continue-on-error: true
    
    - name: Upload safety report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: safety-report
        path: ${{ env.WORKING_DIRECTORY }}/safety-report.json

  integration-test:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    needs: [test, lint]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,test]"
    
    - name: Set up test environment
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        cp .env.example .env
        echo "OPENAI_API_KEY=test-key" >> .env
        echo "SUPABASE_URL=https://test.supabase.co" >> .env
        echo "SUPABASE_SERVICE_ROLE_KEY=test-key" >> .env
    
    - name: Run integration tests
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        pytest tests/integration -v -m "integration"

  build:
    runs-on: ubuntu-latest
    needs: [test, lint, format-check]
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: Install dependencies
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Build Lambda deployment package
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        mkdir -p build
        cp -r src build/
        cp app.py build/
        cp requirements.txt build/
        pip install -r requirements.txt -t build/
        cd build && zip -r ../lambda-deployment.zip .
    
    - name: Upload deployment artifact
      uses: actions/upload-artifact@v3
      with:
        name: lambda-deployment
        path: ${{ env.WORKING_DIRECTORY }}/lambda-deployment.zip
        retention-days: 30

  docker-build:
    runs-on: ubuntu-latest
    needs: [test, lint]
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        docker build -t document-ingestion:${{ github.sha }} .
        docker tag document-ingestion:${{ github.sha }} document-ingestion:latest
    
    - name: Test Docker image
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        # Test that the image can be run
        docker run --rm document-ingestion:latest python -c "print('Docker image works!')"

  deploy-dev:
    runs-on: ubuntu-latest
    needs: [build, integration-test]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Download deployment artifact
      uses: actions/download-artifact@v3
      with:
        name: lambda-deployment
        path: ${{ env.WORKING_DIRECTORY }}
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}
    
    - name: Deploy to AWS Lambda (Development)
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        aws lambda update-function-code \
          --function-name document-ingestion-dev \
          --zip-file fileb://lambda-deployment.zip

  deploy-prod:
    runs-on: ubuntu-latest
    needs: [build, integration-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Download deployment artifact
      uses: actions/download-artifact@v3
      with:
        name: lambda-deployment
        path: ${{ env.WORKING_DIRECTORY }}
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}
    
    - name: Deploy to AWS Lambda (Production)
      working-directory: ${{ env.WORKING_DIRECTORY }}
      run: |
        aws lambda update-function-code \
          --function-name document-ingestion-prod \
          --zip-file fileb://lambda-deployment.zip
