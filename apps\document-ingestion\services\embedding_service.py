"""
Embedding Service for Document Ingestion Pipeline

Handles OpenAI embedding generation with batching, retry logic, and error handling.
"""

import os
import logging
from typing import Dict, List, Any

import openai

from error_handler import OpenAIAPIError, DocumentIngestionError, log_error_details, error_metrics

logger = logging.getLogger(__name__)


class EmbeddingService:
    """Service for generating embeddings using OpenAI API"""
    
    def __init__(self):
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize OpenAI client with API key"""
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            error_metrics.record_error("missing_openai_key")
            raise DocumentIngestionError("OpenAI API key not found")
        
        try:
            self.client = openai.OpenAI(api_key=openai_api_key)
        except Exception as e:
            error_metrics.record_error("openai_client_init_error")
            raise OpenAIAPIError(f"Failed to initialize OpenAI client: {e}")
    
    def get_embedding_config(self) -> Dict[str, Any]:
        """
        Get embedding configuration from environment variables
        
        Returns:
            Dictionary with model and dimensions
        """
        return {
            'model': os.getenv('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small'),
            'dimensions': int(os.getenv('OPENAI_EMBEDDING_DIMENSIONS', '512'))
        }
    
    def generate_embeddings_batch(self, embeddings_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate embeddings for a batch of text data using OpenAI
        
        Args:
            embeddings_data: List of dictionaries with searchable_text
            
        Returns:
            List of dictionaries with added embedding_vector
        """
        try:
            # Extract texts for batch processing
            texts = [item["searchable_text"] for item in embeddings_data]
            
            logger.info(f"Generating embeddings for {len(texts)} texts")
            
            # Process in batches to avoid API limits
            batch_size = 100  # OpenAI allows up to 2048 inputs per request
            results_with_embeddings = []
            
            config = self.get_embedding_config()
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i : i + batch_size]
                batch_data = embeddings_data[i : i + batch_size]
                
                logger.info(
                    f"Processing batch {i // batch_size + 1}/{(len(texts) + batch_size - 1) // batch_size}"
                )
                
                # Generate embeddings for batch with retry logic
                try:
                    response = self.client.embeddings.create(
                        model=config['model'],
                        input=batch_texts,
                        dimensions=config['dimensions'],
                    )
                except Exception as e:
                    error_metrics.record_error("openai_embedding_error")
                    log_error_details(
                        e,
                        {
                            "batch_size": len(batch_texts),
                            "batch_index": i // batch_size + 1,
                            "model": config['model'],
                        },
                    )
                    raise OpenAIAPIError(f"Failed to generate embeddings for batch: {e}")
                
                # Add embeddings to data
                for j, embedding_obj in enumerate(response.data):
                    data_item = batch_data[j].copy()
                    data_item["embedding_vector"] = embedding_obj.embedding
                    results_with_embeddings.append(data_item)
            
            logger.info(f"Generated {len(results_with_embeddings)} embeddings")
            return results_with_embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise DocumentIngestionError(f"Failed to generate embeddings: {e}")
    
    def create_searchable_text(self, product_data: Dict[str, Any]) -> str:
        """
        Create searchable text from product data
        
        Args:
            product_data: Dictionary containing product information
            
        Returns:
            Formatted searchable text string
        """
        # Common field names that should be included in search text
        search_fields = [
            "name",
            "title",
            "product_name",
            "description",
            "desc",
            "product_description",
            "category",
            "type",
            "product_type",
            "brand",
            "manufacturer",
            "tags",
            "keywords",
            "sku",
            "model",
            "product_id",
        ]
        
        text_parts = []
        
        for field in search_fields:
            # Check for exact match or case-insensitive match
            value = None
            for key in product_data.keys():
                if key.lower() == field.lower():
                    value = product_data[key]
                    break
            
            if value is not None and str(value).strip():
                # Format as "Field: Value"
                field_name = field.replace("_", " ").title()
                text_parts.append(f"{field_name}: {str(value).strip()}")
        
        # If no specific fields found, include all non-empty string fields
        if not text_parts:
            for key, value in product_data.items():
                if isinstance(value, str) and value.strip():
                    field_name = key.replace("_", " ").title()
                    text_parts.append(f"{field_name}: {value.strip()}")
        
        return " | ".join(text_parts) if text_parts else "No searchable content" 