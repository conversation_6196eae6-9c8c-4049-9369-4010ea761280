# Document Ingestion Pipeline Deployment Guide

This guide explains how to deploy the document ingestion pipeline with S3 integration using the Terraform infrastructure.

## Overview

The document ingestion pipeline consists of:
- **S3 Bucket**: For file uploads with automatic Lambda triggers
- **Lambda Function**: Processes CSV files and generates embeddings
- **Supabase Integration**: Stores embeddings and metadata
- **OpenAI Integration**: Generates embeddings using OpenAI API

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   S3 Bucket     │────│  Lambda Function│────│   Supabase      │
│   (Upload)      │    │  (Processing)   │    │   (Storage)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│   OpenAI API    │──────────────┘
                        │  (Embeddings)   │
                        └─────────────────┘
```

## Prerequisites

### 1. AWS Secrets Manager Setup

Create the following secrets in AWS Secrets Manager:

```bash
# OpenAI API Key
aws secretsmanager create-secret \
  --name "ezychat/openai-api-key" \
  --description "OpenAI API Key for document ingestion" \
  --secret-string "your-openai-api-key-here"

# Supabase URL
aws secretsmanager create-secret \
  --name "ezychat/supabase-url" \
  --description "Supabase URL for document ingestion" \
  --secret-string "https://your-project.supabase.co"

# Supabase Key
aws secretsmanager create-secret \
  --name "ezychat/supabase-key" \
  --description "Supabase service key for document ingestion" \
  --secret-string "your-supabase-service-key-here"
```

### 2. ECR Repository Setup

Create the ECR repository for the document ingestion Lambda:

```bash
aws ecr create-repository \
  --repository-name lambda-document-ingestion \
  --image-scanning-configuration scanOnPush=true \
  --encryption-configuration encryptionType=AES256
```

### 3. Build and Push Lambda Container

```bash
# Build the container
cd apps/document-ingestion
docker build -t lambda-document-ingestion .

# Tag and push to ECR
aws ecr get-login-password --region ap-southeast-5 | docker login --username AWS --password-stdin 381491882604.dkr.ecr.ap-southeast-5.amazonaws.com
docker tag lambda-document-ingestion:latest 381491882604.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-document-ingestion:latest
docker push 381491882604.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-document-ingestion:latest
```

## Deployment Steps

### 1. Environment Configuration

Update the environment-specific variables in your Terraform configuration:

```hcl
# For UAT environment (iac/aws-ezychat-uat-tf/main.tf)
module "core" {
  source = "../core"
  
  env = "uat"
  
  # Document ingestion secrets
  openai_api_key_secret_arn = "arn:aws:secretsmanager:ap-southeast-5:381491882604:secret:ezychat/openai-api-key-xxxxx"
  supabase_url_secret_arn   = "arn:aws:secretsmanager:ap-southeast-5:381491882604:secret:ezychat/supabase-url-xxxxx"
  supabase_key_secret_arn   = "arn:aws:secretsmanager:ap-southeast-5:381491882604:secret:ezychat/supabase-key-xxxxx"
  
  tags = {
    Environment = "UAT"
    Service     = "EzyChat"
  }
}
```

### 2. Deploy Infrastructure

```bash
# Navigate to the environment directory
cd infra/iac/aws-ezychat-uat-tf

# Initialize Terraform
terraform init

# Plan the deployment
terraform plan

# Apply the changes
terraform apply
```

### 3. Verify Deployment

Check the outputs to verify the deployment:

```bash
terraform output
```

Expected outputs:
- `lambda_platform`: Lambda function information
- `document_storage`: S3 bucket information

## Usage

### 1. File Upload Structure

Upload CSV files to the S3 bucket using this structure:

```
s3://{bucket-name}/
├── {user_id}/
│   ├── uploaded/
│   │   ├── products.csv
│   │   └── inventory.csv
│   ├── processed/     # Successfully processed files
│   └── failed/        # Files that failed processing
```

### 2. Example Upload

```bash
# Upload a CSV file for user "user123"
aws s3 cp products.csv s3://uat-ezychat-documents-xxxxx/user123/uploaded/products.csv
```

### 3. Monitor Processing

Check CloudWatch logs for the Lambda function:

```bash
# Get the Lambda function name
aws lambda list-functions --query 'Functions[?contains(FunctionName, `document-ingestion`)].FunctionName'

# View logs
aws logs tail /aws/lambda/uat-lambda-api-document-ingestion-lambda --follow
```

## Configuration

### Lambda Function Configuration

The Lambda function is configured in `configs/lambda_services.yaml`:

```yaml
document-ingestion-lambda:
  image_repository_uri: 381491882604.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-document-ingestion:latest
  timeout: 900  # 15 minutes
  memory_size: 1024  # 1GB
  environment_variables:
    NODE_ENV: "production"
    LOG_LEVEL: "info"
    OPENAI_API_KEY: "{{OPENAI_API_KEY_ARN}}"
    SUPABASE_URL: "{{SUPABASE_URL_ARN}}"
    SUPABASE_KEY: "{{SUPABASE_KEY_ARN}}"
```

### S3 Bucket Configuration

The S3 bucket is configured with:
- **Versioning**: Enabled for data protection
- **Lifecycle**: Automatic storage class transitions
- **CORS**: Configured for web uploads
- **Security**: SSL-only access, private bucket
- **Events**: Triggers Lambda on CSV uploads

## Monitoring and Troubleshooting

### 1. CloudWatch Metrics

Monitor these metrics:
- **Lambda**: Duration, errors, throttles
- **S3**: Request counts, errors
- **API Gateway**: Request counts, 4xx/5xx errors

### 2. Common Issues

#### Lambda Timeout
- **Cause**: Large CSV files taking too long to process
- **Solution**: Increase timeout in configuration or split large files

#### Memory Issues
- **Cause**: Insufficient memory for embedding generation
- **Solution**: Increase memory_size in configuration

#### S3 Permission Errors
- **Cause**: Lambda doesn't have proper S3 permissions
- **Solution**: Check IAM policy in lambda_services.yaml

#### OpenAI API Errors
- **Cause**: Invalid API key or rate limits
- **Solution**: Verify secret in AWS Secrets Manager

### 3. Log Analysis

```bash
# Search for errors in Lambda logs
aws logs filter-log-events \
  --log-group-name /aws/lambda/uat-lambda-api-document-ingestion-lambda \
  --filter-pattern "ERROR"
```

## Security Considerations

### 1. IAM Permissions
- Lambda has minimal required permissions
- S3 bucket is private with controlled access
- Secrets are stored in AWS Secrets Manager

### 2. Data Protection
- All data in transit uses HTTPS
- S3 bucket enforces SSL-only access
- File versioning enabled for data recovery

### 3. Cost Optimization
- S3 lifecycle policies for automatic storage transitions
- Lambda timeout and memory optimized for cost
- CloudWatch log retention set to 14 days

## Scaling Considerations

### 1. Lambda Concurrency
- Default limit: 1000 concurrent executions
- Monitor CloudWatch metrics for throttling
- Request limit increase if needed

### 2. S3 Performance
- S3 automatically scales to handle concurrent uploads
- Consider S3 Transfer Acceleration for global uploads
- Monitor S3 request metrics

### 3. OpenAI API Limits
- Monitor OpenAI API rate limits
- Implement exponential backoff in Lambda
- Consider batch processing for large files

## Production Deployment

For production deployment:

1. **Update Secrets**: Use production API keys and Supabase credentials
2. **Increase Limits**: Request higher Lambda concurrency limits
3. **Monitoring**: Set up CloudWatch alarms for errors and performance
4. **Backup**: Configure S3 cross-region replication if needed
5. **Security**: Review and tighten IAM permissions

## Support

For issues or questions:
1. Check CloudWatch logs for error details
2. Verify AWS Secrets Manager configuration
3. Test with small CSV files first
4. Monitor AWS service quotas and limits 