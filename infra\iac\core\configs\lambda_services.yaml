# Lambda Services Configuration
# This file defines all Lambda services deployed across environments.
# Environment-specific settings are handled via Terraform variables.

sample-lambda:
  # Container configuration
  image_repository_uri: ************.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-nodejs-sample:latest

  # Environment variables (AWS_REGION is automatically set by Lambda)
  environment_variables:
    NODE_ENV: "production"
    LOG_LEVEL: "info"

  # API Gateway path patterns (like ECS) - simple and intuitive
  api_gateway:
    path_patterns:
      - "/sample"    # Exact match for /sample
      - "/sample/*"  # Matches /sample/* (any HTTP method)

  # Tags
  tags:
    Service: "Sample Lambda"
    Team: "Platform"
    ECRAccount: "************"

# Document Ingestion Pipeline
document-ingestion-lambda:
  image_repository_uri: ************.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-document-ingestion:latest

  # Environment variables for document processing
  environment_variables:
    LOG_LEVEL: "info"
    OPENAI_API_KEY: "{{OPENAI_API_KEY_ARN}}"
    SUPABASE_URL: "{{SUPABASE_URL_ARN}}"
    SUPABASE_KEY: "{{SUPABASE_KEY_ARN}}"

  # Lambda configuration for document processing
  timeout: 900  # 15 minutes for large file processing
  memory_size: 1024  # 1GB for embedding generation

  # IAM policy for S3 access and other permissions
  iam_policy: |
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": [
            "s3:GetObject",
            "s3:PutObject",
            "s3:DeleteObject",
            "s3:ListBucket"
          ],
          "Resource": [
            "arn:aws:s3:::{{DOCUMENT_BUCKET_NAME}}",
            "arn:aws:s3:::{{DOCUMENT_BUCKET_NAME}}/*"
          ]
        },
        {
          "Effect": "Allow",
          "Action": [
            "secretsmanager:GetSecretValue"
          ],
          "Resource": [
            "{{OPENAI_API_KEY_ARN}}",
            "{{SUPABASE_URL_ARN}}",
            "{{SUPABASE_KEY_ARN}}"
          ]
        }
      ]
    }

  # Tags
  tags:
    Service: "Document Ingestion Lambda"
    Team: "Platform"
    ECRAccount: "************"