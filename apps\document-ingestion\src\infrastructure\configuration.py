"""
Configuration Management for Document Ingestion Pipeline

This module provides configuration management using environment variables
with proper validation and default values.
"""

import os
from typing import Optional
from ..domain.interfaces import IConfiguration


class EnvironmentConfiguration(IConfiguration):
    """Configuration implementation using environment variables"""
    
    def __init__(self):
        self._validate_required_config()
    
    def _validate_required_config(self) -> None:
        """Validate that all required configuration is present"""
        # Skip validation in test environment
        if os.getenv('ENVIRONMENT') == 'test' or os.getenv('PYTEST_CURRENT_TEST'):
            return

        required_vars = [
            'OPENAI_API_KEY',
            'SUPABASE_URL',
            'SUPABASE_SERVICE_ROLE_KEY'
        ]

        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
    
    def get_openai_api_key(self) -> str:
        """Get OpenAI API key"""
        return os.getenv('OPENAI_API_KEY', '')
    
    def get_openai_model(self) -> str:
        """Get OpenAI embedding model"""
        return os.getenv('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small')
    
    def get_openai_dimensions(self) -> int:
        """Get OpenAI embedding dimensions"""
        return int(os.getenv('OPENAI_EMBEDDING_DIMENSIONS', '512'))
    
    def get_supabase_url(self) -> str:
        """Get Supabase URL"""
        return os.getenv('SUPABASE_URL', '')
    
    def get_supabase_service_key(self) -> str:
        """Get Supabase service role key"""
        return os.getenv('SUPABASE_SERVICE_ROLE_KEY', '')
    
    def get_batch_size(self) -> int:
        """Get batch processing size"""
        return int(os.getenv('BATCH_SIZE', '100'))
    
    def get_max_file_size(self) -> int:
        """Get maximum file size limit in bytes"""
        return int(os.getenv('MAX_FILE_SIZE_BYTES', str(50 * 1024 * 1024)))  # 50MB default
    
    def get_max_rows(self) -> int:
        """Get maximum rows limit"""
        return int(os.getenv('MAX_ROWS', '100000'))
    
    def get_log_level(self) -> str:
        """Get log level"""
        return os.getenv('LOG_LEVEL', 'INFO')
    
    def get_environment(self) -> str:
        """Get environment name"""
        return os.getenv('ENVIRONMENT', 'dev')
    
    def get_service_version(self) -> str:
        """Get service version"""
        return os.getenv('SERVICE_VERSION', '1.0.0')
    
    def get_retry_attempts(self) -> int:
        """Get retry attempts for operations"""
        return int(os.getenv('RETRY_ATTEMPTS', '3'))
    
    def get_retry_min_wait(self) -> int:
        """Get minimum retry wait time in seconds"""
        return int(os.getenv('RETRY_MIN_WAIT', '1'))
    
    def get_retry_max_wait(self) -> int:
        """Get maximum retry wait time in seconds"""
        return int(os.getenv('RETRY_MAX_WAIT', '60'))
    
    def get_embedding_batch_size(self) -> int:
        """Get embedding batch size for OpenAI API"""
        return int(os.getenv('EMBEDDING_BATCH_SIZE', '100'))
    
    def get_supabase_batch_size(self) -> int:
        """Get Supabase batch insert size"""
        return int(os.getenv('SUPABASE_BATCH_SIZE', '100'))
    
    def get_similarity_threshold(self) -> float:
        """Get default similarity threshold for vector search"""
        return float(os.getenv('SIMILARITY_THRESHOLD', '0.7'))
    
    def get_max_search_results(self) -> int:
        """Get maximum search results"""
        return int(os.getenv('MAX_SEARCH_RESULTS', '10'))
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.get_environment().lower() in ['dev', 'development', 'local']
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.get_environment().lower() in ['prod', 'production']
    
    def get_all_config(self) -> dict:
        """Get all configuration as dictionary for debugging"""
        return {
            'environment': self.get_environment(),
            'service_version': self.get_service_version(),
            'log_level': self.get_log_level(),
            'openai_model': self.get_openai_model(),
            'openai_dimensions': self.get_openai_dimensions(),
            'batch_size': self.get_batch_size(),
            'max_file_size': self.get_max_file_size(),
            'max_rows': self.get_max_rows(),
            'retry_attempts': self.get_retry_attempts(),
            'embedding_batch_size': self.get_embedding_batch_size(),
            'supabase_batch_size': self.get_supabase_batch_size(),
            'similarity_threshold': self.get_similarity_threshold(),
            'max_search_results': self.get_max_search_results(),
            # Don't include sensitive values like API keys
            'has_openai_key': bool(self.get_openai_api_key()),
            'has_supabase_url': bool(self.get_supabase_url()),
            'has_supabase_key': bool(self.get_supabase_service_key()),
        }
