"""
Domain Interfaces for Document Ingestion Pipeline

This module defines the interfaces (ports) that the domain layer expects
from the infrastructure layer, following the Dependency Inversion Principle.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from .entities import (
    Document, DocumentId, UserId, S3Location, ProductData, 
    EmbeddingVector, ProductEmbedding, ProcessingResult
)


class IDocumentRepository(ABC):
    """Interface for document persistence operations"""
    
    @abstractmethod
    async def save(self, document: Document) -> None:
        """Save or update a document"""
        pass
    
    @abstractmethod
    async def find_by_id(self, document_id: DocumentId, user_id: UserId) -> Optional[Document]:
        """Find document by ID and user ID"""
        pass
    
    @abstractmethod
    async def find_by_user(self, user_id: UserId) -> List[Document]:
        """Find all documents for a user"""
        pass


class IProductEmbeddingRepository(ABC):
    """Interface for product embedding persistence operations"""
    
    @abstractmethod
    async def save_batch(self, embeddings: List[ProductEmbedding]) -> None:
        """Save a batch of product embeddings"""
        pass
    
    @abstractmethod
    async def find_by_document(self, document_id: DocumentId, user_id: UserId) -> List[ProductEmbedding]:
        """Find all embeddings for a document"""
        pass
    
    @abstractmethod
    async def search_similar(self, user_id: UserId, query_vector: List[float], 
                           similarity_threshold: float, max_results: int) -> List[Dict[str, Any]]:
        """Search for similar products using vector similarity"""
        pass


class IFileStorage(ABC):
    """Interface for file storage operations"""
    
    @abstractmethod
    async def download_content(self, location: S3Location) -> bytes:
        """Download file content from storage"""
        pass
    
    @abstractmethod
    async def move_file(self, source: S3Location, target_folder: str, user_id: UserId) -> bool:
        """Move file between folders"""
        pass
    
    @abstractmethod
    def extract_user_info(self, key: str) -> Tuple[Optional[UserId], Optional[Dict[str, str]]]:
        """Extract user information from storage key"""
        pass
    
    @abstractmethod
    def validate_file_format(self, key: str) -> bool:
        """Validate file format"""
        pass


class IEmbeddingService(ABC):
    """Interface for embedding generation"""
    
    @abstractmethod
    async def generate_embeddings(self, texts: List[str]) -> List[EmbeddingVector]:
        """Generate embeddings for a list of texts"""
        pass
    
    @abstractmethod
    def get_embedding_config(self) -> Dict[str, Any]:
        """Get embedding configuration"""
        pass


class ICSVProcessor(ABC):
    """Interface for CSV processing operations"""
    
    @abstractmethod
    async def parse_csv(self, content: bytes) -> Tuple[List[str], List[ProductData]]:
        """Parse CSV content and return column names and product data"""
        pass
    
    @abstractmethod
    def validate_csv_data(self, data: List[ProductData]) -> bool:
        """Validate CSV data"""
        pass
    
    @abstractmethod
    def clean_csv_data(self, data: List[ProductData]) -> List[ProductData]:
        """Clean CSV data"""
        pass


class IEventPublisher(ABC):
    """Interface for publishing domain events"""
    
    @abstractmethod
    async def publish_document_processing_started(self, document: Document) -> None:
        """Publish document processing started event"""
        pass
    
    @abstractmethod
    async def publish_document_processing_completed(self, result: ProcessingResult) -> None:
        """Publish document processing completed event"""
        pass
    
    @abstractmethod
    async def publish_document_processing_failed(self, result: ProcessingResult) -> None:
        """Publish document processing failed event"""
        pass


class ILogger(ABC):
    """Interface for logging operations"""
    
    @abstractmethod
    def info(self, message: str, **context) -> None:
        """Log info message"""
        pass
    
    @abstractmethod
    def warning(self, message: str, **context) -> None:
        """Log warning message"""
        pass
    
    @abstractmethod
    def error(self, message: str, error: Optional[Exception] = None, **context) -> None:
        """Log error message"""
        pass
    
    @abstractmethod
    def debug(self, message: str, **context) -> None:
        """Log debug message"""
        pass


class IMetricsCollector(ABC):
    """Interface for metrics collection"""
    
    @abstractmethod
    def record_processing_time(self, operation: str, duration_ms: float, **tags) -> None:
        """Record processing time metric"""
        pass
    
    @abstractmethod
    def record_error(self, error_type: str, **tags) -> None:
        """Record error metric"""
        pass
    
    @abstractmethod
    def record_success(self, operation: str, **tags) -> None:
        """Record success metric"""
        pass
    
    @abstractmethod
    def increment_counter(self, metric_name: str, **tags) -> None:
        """Increment counter metric"""
        pass


class IConfiguration(ABC):
    """Interface for configuration management"""
    
    @abstractmethod
    def get_openai_api_key(self) -> str:
        """Get OpenAI API key"""
        pass
    
    @abstractmethod
    def get_openai_model(self) -> str:
        """Get OpenAI embedding model"""
        pass
    
    @abstractmethod
    def get_openai_dimensions(self) -> int:
        """Get OpenAI embedding dimensions"""
        pass
    
    @abstractmethod
    def get_supabase_url(self) -> str:
        """Get Supabase URL"""
        pass
    
    @abstractmethod
    def get_supabase_service_key(self) -> str:
        """Get Supabase service role key"""
        pass
    
    @abstractmethod
    def get_batch_size(self) -> int:
        """Get batch processing size"""
        pass
    
    @abstractmethod
    def get_max_file_size(self) -> int:
        """Get maximum file size limit"""
        pass
    
    @abstractmethod
    def get_max_rows(self) -> int:
        """Get maximum rows limit"""
        pass
