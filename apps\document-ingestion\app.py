"""
AWS Lambda Function for Multi-Tenant CSV Document Ingestion Pipeline

This Lambda function processes CSV files uploaded to S3, extracts product data,
generates embeddings using OpenAI, and stores the results in Supabase with pgvector.

Architecture:
- S3 trigger on CSV upload
- Multi-tenant isolation by user_id in S3 path
- Batch processing for OpenAI embeddings
- Supabase storage with vector search capabilities
- Error handling and file movement between S3 folders

S3 Path Structure:
- Upload: s3://bucket/{user_id}/uploaded/{filename}.csv
- Processed: s3://bucket/{user_id}/processed/{filename}.csv
- Failed: s3://bucket/{user_id}/failed/{filename}.csv
"""

import json
import logging
import os
import re
import time
import uuid

from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import unquote_plus

import boto3
import pandas as pd
from botocore.exceptions import ClientError
import openai
from supabase_client import SupabaseClient
from error_handler import (
    log_error_details,
    validate_csv_data,
    clean_csv_data,
    error_metrics,
    CSVValidationError,
    S3AccessError,
    OpenAIAPIError,
)
from logging_config import setup_lambda_logging

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
s3_client = boto3.client("s3")


class DocumentIngestionError(Exception):
    """Custom exception for document ingestion errors"""


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Main Lambda handler for S3-triggered CSV processing and API Gateway requests

    Args:
        event: S3 event notification or API Gateway event
        context: Lambda context

    Returns:
        Response dictionary with processing results
    """
    # Setup structured logging with Lambda context
    structured_logger = setup_lambda_logging(context)

    structured_logger.logger.info(
        "Lambda function invoked",
        extra={
            "operation": "lambda_handler",
            "status": "started",
            "event_type": "api_gateway" if "httpMethod" in event else "s3_event",
            "metrics": {
                "record_count": len(event.get("Records", []))
                if "Records" in event
                else 1
            },
        },
    )

    start_time = time.time()

    try:
        # Check if this is an API Gateway event (for testing)
        if "httpMethod" in event:
            return handle_api_gateway_request(
                event, context, structured_logger, start_time
            )

        # Handle S3 event (production use case)
        return handle_s3_event(event, context, structured_logger, start_time)

    except Exception as e:
        duration_ms = (time.time() - start_time) * 1000

        structured_logger.logger.error(
            "Lambda function failed",
            extra={
                "operation": "lambda_handler",
                "status": "failed",
                "duration_ms": duration_ms,
                "error_code": type(e).__name__,
            },
            exc_info=True,
        )

        error_metrics.record_error("lambda_handler_error")

        return {
            "statusCode": 500,
            "body": json.dumps({"error": str(e), "message": "Processing failed"}),
        }


def handle_api_gateway_request(
    event: Dict[str, Any], context: Any, structured_logger, start_time: float
) -> Dict[str, Any]:
    """
    Handle API Gateway requests (for testing and health checks)

    Args:
        event: API Gateway event
        context: Lambda context
        structured_logger: Configured logger
        start_time: Function start time

    Returns:
        API Gateway response
    """
    duration_ms = (time.time() - start_time) * 1000

    structured_logger.logger.info(
        "API Gateway request handled",
        extra={
            "operation": "api_gateway_handler",
            "status": "completed",
            "duration_ms": duration_ms,
            "path": event.get("path", ""),
            "http_method": event.get("httpMethod", ""),
        },
    )

    return {
        "statusCode": 200,
        "headers": {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type",
            "Access-Control-Allow-Methods": "GET,POST,OPTIONS",
        },
        "body": json.dumps(
            {
                "message": "Document ingestion Lambda is running",
                "status": "healthy",
                "timestamp": time.time(),
                "path": event.get("path", ""),
                "method": event.get("httpMethod", ""),
                "event_type": "api_gateway",
            }
        ),
    }


def handle_s3_event(
    event: Dict[str, Any], context: Any, structured_logger, start_time: float
) -> Dict[str, Any]:
    """
    Handle S3 event notifications (production use case)

    Args:
        event: S3 event notification
        context: Lambda context
        structured_logger: Configured logger
        start_time: Function start time

    Returns:
        Response dictionary with processing results
    """
    # Process each S3 record in the event
    results = []
    for record in event.get("Records", []):
        if record.get("eventSource") == "aws:s3":
            result = process_s3_record(record)
            results.append(result)

    duration_ms = (time.time() - start_time) * 1000

    structured_logger.logger.info(
        "S3 event processing completed",
        extra={
            "operation": "s3_event_handler",
            "status": "completed",
            "duration_ms": duration_ms,
            "metrics": {
                "processed_records": len(results),
                "successful_records": sum(
                    1 for r in results if r.get("success", False)
                ),
            },
        },
    )

    return {
        "statusCode": 200,
        "body": json.dumps({"message": "S3 processing completed", "results": results}),
    }


def process_s3_record(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a single S3 record from the event

    Args:
        record: S3 event record

    Returns:
        Processing result dictionary
    """
    try:
        # Extract S3 information
        bucket = record["s3"]["bucket"]["name"]
        key = unquote_plus(record["s3"]["object"]["key"])

        logger.info(f"Processing S3 object: s3://{bucket}/{key}")

        # Validate and extract user information
        user_id, file_info = extract_user_info_from_s3_key(key)

        if not user_id:
            raise DocumentIngestionError(f"Invalid S3 key format: {key}")

        # Validate file format
        if not key.lower().endswith(".csv"):
            raise DocumentIngestionError(f"Unsupported file format: {key}")

        # Generate unique document ID
        document_id = generate_document_id()

        logger.info(f"Processing document {document_id} for user {user_id}")

        # Initialize Supabase client
        supabase_client = SupabaseClient()

        # Create document record
        supabase_client.create_document(
            user_id=user_id,
            document_id=document_id,
            s3_key=key,
            original_filename=file_info["filename"],
            column_names=[],  # Will be updated after CSV parsing
            total_rows=0,  # Will be updated after CSV parsing
        )

        # Update status to processing
        supabase_client.update_document_status(
            user_id=user_id, document_id=document_id, status="processing"
        )

        # Process the CSV file
        processing_result = process_csv_file(
            bucket=bucket,
            key=key,
            user_id=user_id,
            document_id=document_id,
            supabase_client=supabase_client,
        )

        # Move file to processed folder
        move_s3_file(bucket, key, user_id, "processed")

        # Update status to completed
        supabase_client.update_document_status(
            user_id=user_id, document_id=document_id, status="completed"
        )

        return {
            "success": True,
            "user_id": user_id,
            "document_id": document_id,
            "processed_rows": processing_result["processed_rows"],
            "message": "Document processed successfully",
        }

    except Exception as e:
        logger.error(f"Error processing S3 record: {e}")

        # Try to update document status to failed
        try:
            if "user_id" in locals() and "document_id" in locals():
                supabase_client.update_document_status(
                    user_id=user_id,
                    document_id=document_id,
                    status="failed",
                    error_message=str(e),
                )

                # Move file to failed folder
                move_s3_file(bucket, key, user_id, "failed")
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")

        return {
            "success": False,
            "error": str(e),
            "message": "Document processing failed",
        }


def extract_user_info_from_s3_key(
    s3_key: str,
) -> Tuple[Optional[str], Optional[Dict[str, str]]]:
    """
    Extract user ID and file information from S3 key

    Expected format: {user_id}/uploaded/{filename}.csv

    Args:
        s3_key: S3 object key

    Returns:
        Tuple of (user_id, file_info) or (None, None) if invalid
    """
    try:
        # Pattern: {user_id}/uploaded/{filename}.csv
        pattern = r"^([^/]+)/uploaded/(.+\.csv)$"
        match = re.match(pattern, s3_key)

        if match:
            user_id = match.group(1)
            filename = match.group(2)

            return user_id, {"filename": filename, "folder": "uploaded"}

        logger.warning(f"S3 key does not match expected pattern: {s3_key}")
        return None, None

    except Exception as e:
        logger.error(f"Error extracting user info from S3 key {s3_key}: {e}")
        return None, None


def generate_document_id() -> str:
    """
    Generate unique document ID using UUID

    Returns:
        Unique document ID (UUID)
    """
    # Generate a clean UUID for document identification
    return str(uuid.uuid4())


def move_s3_file(
    bucket: str, source_key: str, user_id: str, target_folder: str
) -> bool:
    """
    Move S3 file between folders with comprehensive error handling

    Args:
        bucket: S3 bucket name
        source_key: Source S3 key
        user_id: User identifier
        target_folder: Target folder (processed/failed)

    Returns:
        True if successful
    """
    try:
        # Extract filename from source key
        filename = os.path.basename(source_key)

        # Construct target key
        target_key = f"{user_id}/{target_folder}/{filename}"

        logger.info(f"Moving S3 file from {source_key} to {target_key}")

        # Check if source file exists
        try:
            s3_client.head_object(Bucket=bucket, Key=source_key)
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                logger.warning(f"Source file not found: {source_key}")
                return False
            else:
                raise

        # Copy object to new location
        copy_source = {"Bucket": bucket, "Key": source_key}
        s3_client.copy_object(CopySource=copy_source, Bucket=bucket, Key=target_key)

        # Verify copy was successful
        try:
            s3_client.head_object(Bucket=bucket, Key=target_key)
        except ClientError as e:
            error_metrics.record_error("s3_copy_verification_failed")
            raise S3AccessError(f"Failed to verify copied file: {e}")

        # Delete original object
        s3_client.delete_object(Bucket=bucket, Key=source_key)

        # Verify deletion
        try:
            s3_client.head_object(Bucket=bucket, Key=source_key)
            # If we get here, the file still exists
            logger.warning(f"Original file still exists after deletion: {source_key}")
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                # File successfully deleted
                pass
            else:
                logger.warning(f"Error verifying file deletion: {e}")

        logger.info(f"Successfully moved file from {source_key} to {target_key}")
        error_metrics.record_success()
        return True

    except ClientError as e:
        error_code = e.response.get("Error", {}).get("Code", "Unknown")
        error_metrics.record_error(f"s3_error_{error_code}")
        log_error_details(
            e,
            {
                "operation": "move_s3_file",
                "source_key": source_key,
                "target_folder": target_folder,
                "user_id": user_id,
            },
        )
        logger.error(f"Error moving S3 file {source_key}: {e}")
        return False
    except Exception as e:
        error_metrics.record_error("s3_move_unexpected_error")
        log_error_details(
            e,
            {
                "operation": "move_s3_file",
                "source_key": source_key,
                "target_folder": target_folder,
                "user_id": user_id,
            },
        )
        logger.error(f"Unexpected error moving S3 file {source_key}: {e}")
        return False


def process_csv_file(
    bucket: str,
    key: str,
    user_id: str,
    document_id: str,
    supabase_client: SupabaseClient,
) -> Dict[str, Any]:
    """
    Process CSV file: parse, convert to documents, generate embeddings, store in Supabase

    Args:
        bucket: S3 bucket name
        key: S3 object key
        user_id: User identifier
        document_id: Document identifier
        supabase_client: Supabase client instance

    Returns:
        Processing result dictionary
    """
    try:
        # Download CSV file from S3
        logger.info(f"Downloading CSV file: s3://{bucket}/{key}")
        response = s3_client.get_object(Bucket=bucket, Key=key)
        csv_content = response["Body"].read()

        # Parse CSV with pandas
        try:
            df = pd.read_csv(pd.io.common.BytesIO(csv_content))
        except Exception as e:
            error_metrics.record_error("csv_parsing_error")
            raise CSVValidationError(f"Failed to parse CSV: {e}")

        # Validate and clean CSV data
        validate_csv_data(df)
        df = clean_csv_data(df)

        logger.info(
            f"Processed CSV with {len(df)} rows and {len(df.columns)} columns after cleaning"
        )

        # Update document with column information
        column_names = df.columns.tolist()
        supabase_client.update_document_status(
            user_id=user_id, document_id=document_id, status="processing"
        )

        # Update document with CSV metadata
        supabase_client.client.table("documents").update(
            {"column_names": column_names, "total_rows": len(df)}
        ).eq("user_id", user_id).eq("document_id", document_id).execute()

        # Process rows and generate embeddings
        embeddings_data = []

        for index, row in df.iterrows():
            # Convert row to product document
            product_data = row.to_dict()

            # Create searchable text
            searchable_text = create_searchable_text(product_data)

            # Generate embedding (will be done in batch later)
            embeddings_data.append(
                {
                    "row_index": index,
                    "product_data": product_data,
                    "searchable_text": searchable_text,
                }
            )

        # Generate embeddings in batches
        embeddings_with_vectors = generate_embeddings_batch(embeddings_data)

        # Store embeddings in Supabase
        supabase_client.batch_insert_embeddings(
            user_id=user_id,
            document_id=document_id,
            embeddings_data=embeddings_with_vectors,
        )

        return {
            "processed_rows": len(df),
            "columns": column_names,
            "embeddings_generated": len(embeddings_with_vectors),
        }

    except Exception as e:
        logger.error(f"Error processing CSV file {key}: {e}")
        raise DocumentIngestionError(f"Failed to process CSV file: {e}")


def create_searchable_text(product_data: Dict[str, Any]) -> str:
    """
    Create searchable text from product data

    Args:
        product_data: Dictionary containing product information

    Returns:
        Formatted searchable text string
    """
    # Common field names that should be included in search text
    search_fields = [
        "name",
        "title",
        "product_name",
        "description",
        "desc",
        "product_description",
        "category",
        "type",
        "product_type",
        "brand",
        "manufacturer",
        "tags",
        "keywords",
        "sku",
        "model",
        "product_id",
    ]

    text_parts = []

    for field in search_fields:
        # Check for exact match or case-insensitive match
        value = None
        for key in product_data.keys():
            if key.lower() == field.lower():
                value = product_data[key]
                break

        if value is not None and str(value).strip():
            # Format as "Field: Value"
            field_name = field.replace("_", " ").title()
            text_parts.append(f"{field_name}: {str(value).strip()}")

    # If no specific fields found, include all non-empty string fields
    if not text_parts:
        for key, value in product_data.items():
            if isinstance(value, str) and value.strip():
                field_name = key.replace("_", " ").title()
                text_parts.append(f"{field_name}: {value.strip()}")

    return " | ".join(text_parts) if text_parts else "No searchable content"


def generate_embeddings_batch(
    embeddings_data: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """
    Generate embeddings for a batch of text data using OpenAI

    Args:
        embeddings_data: List of dictionaries with searchable_text

    Returns:
        List of dictionaries with added embedding_vector
    """
    try:
        # Initialize OpenAI client
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            error_metrics.record_error("missing_openai_key")
            raise DocumentIngestionError("OpenAI API key not found")

        try:
            client = openai.OpenAI(api_key=openai_api_key)
        except Exception as e:
            error_metrics.record_error("openai_client_init_error")
            raise OpenAIAPIError(f"Failed to initialize OpenAI client: {e}")

        # Extract texts for batch processing
        texts = [item["searchable_text"] for item in embeddings_data]

        logger.info(f"Generating embeddings for {len(texts)} texts")

        # Process in batches to avoid API limits
        batch_size = 100  # OpenAI allows up to 2048 inputs per request
        results_with_embeddings = []

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i : i + batch_size]
            batch_data = embeddings_data[i : i + batch_size]

            logger.info(
                f"Processing batch {i // batch_size + 1}/{(len(texts) + batch_size - 1) // batch_size}"
            )

            # Get embedding model and dimensions from environment variables
            embedding_model = os.getenv('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small')
            embedding_dimensions = int(os.getenv('OPENAI_EMBEDDING_DIMENSIONS', '512'))
            
            # Generate embeddings for batch with retry logic
            try:
                response = client.embeddings.create(
                    model=embedding_model,
                    input=batch_texts,
                    dimensions=embedding_dimensions,  # Configurable via env var
                )
            except Exception as e:
                error_metrics.record_error("openai_embedding_error")
                log_error_details(
                    e,
                    {
                        "batch_size": len(batch_texts),
                        "batch_index": i // batch_size + 1,
                        "model": embedding_model,
                    },
                )
                raise OpenAIAPIError(f"Failed to generate embeddings for batch: {e}")

            # Add embeddings to data
            for j, embedding_obj in enumerate(response.data):
                data_item = batch_data[j].copy()
                data_item["embedding_vector"] = embedding_obj.embedding
                results_with_embeddings.append(data_item)

        logger.info(f"Generated {len(results_with_embeddings)} embeddings")
        return results_with_embeddings

    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise DocumentIngestionError(f"Failed to generate embeddings: {e}")
