"""
AWS Lambda Function for Multi-Tenant CSV Document Ingestion Pipeline

This Lambda function processes CSV files uploaded to S3, extracts product data,
generates embeddings using OpenAI, and stores the results in Supabase with pgvector.

REFACTORED TO CLEAN ARCHITECTURE:
- Domain layer: Business entities and rules
- Application layer: Use cases and orchestration
- Infrastructure layer: External dependencies
- Presentation layer: Lambda handlers

Architecture:
- S3 trigger on CSV upload
- Multi-tenant isolation by user_id in S3 path
- Batch processing for OpenAI embeddings
- Supabase storage with vector search capabilities
- Error handling and file movement between S3 folders

S3 Path Structure:
- Upload: s3://bucket/{user_id}/uploaded/{filename}.csv
- Processed: s3://bucket/{user_id}/processed/{filename}.csv
- Failed: s3://bucket/{user_id}/failed/{filename}.csv
"""

from typing import Dict, Any
from src.presentation.lambda_handler import lambda_handler as clean_lambda_handler


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Main Lambda handler - delegates to clean architecture implementation

    This function serves as the entry point and delegates to the clean architecture
    implementation in the presentation layer.

    Args:
        event: S3 event notification or API Gateway event
        context: Lambda context

    Returns:
        Response dictionary with processing results
    """
    return clean_lambda_handler(event, context)



# Legacy code has been refactored to clean architecture
# See src/ directory for the new implementation:
# - src/domain/: Business entities and rules
# - src/application/: Use cases and orchestration
# - src/infrastructure/: External dependencies and adapters
# - src/presentation/: Lambda handlers and controllers
#
# The main benefits of this refactoring:
# 1. Separation of concerns
# 2. Dependency inversion
# 3. Testability
# 4. Maintainability
# 5. Clear boundaries between layers





