# PowerShell Development Environment Setup Script
# Document Ingestion Pipeline - Clean Architecture

param(
    [switch]$SkipTests,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Test-PythonVersion {
    Write-Status "Checking Python version..."
    
    try {
        $pythonVersion = python --version 2>&1
        if ($pythonVersion -match "Python 3\.12") {
            Write-Success "Python 3.12 found: $pythonVersion"
            return $true
        } else {
            Write-Error "Python 3.12 is required. Found: $pythonVersion"
            return $false
        }
    } catch {
        Write-Error "Python is not installed or not in PATH"
        return $false
    }
}

function New-VirtualEnvironment {
    Write-Status "Creating virtual environment..."
    
    if (Test-Path ".venv") {
        Write-Warning "Virtual environment already exists. Removing old one..."
        Remove-Item -Recurse -Force ".venv"
    }
    
    python -m venv .venv
    Write-Success "Virtual environment created"
}

function Enable-VirtualEnvironment {
    Write-Status "Activating virtual environment..."
    
    & ".venv\Scripts\Activate.ps1"
    Write-Success "Virtual environment activated"
}

function Install-Dependencies {
    Write-Status "Installing dependencies..."
    
    # Upgrade pip
    python -m pip install --upgrade pip
    
    # Install development dependencies
    pip install -e ".[dev]"
    
    Write-Success "Dependencies installed"
}

function Set-EnvironmentFile {
    Write-Status "Setting up environment file..."
    
    if (-not (Test-Path ".env")) {
        Copy-Item ".env.example" ".env"
        Write-Success "Created .env file from .env.example"
        Write-Warning "Please edit .env file with your actual API keys and configuration"
    } else {
        Write-Warning ".env file already exists. Skipping..."
    }
}

function Install-PreCommitHooks {
    Write-Status "Setting up pre-commit hooks..."
    
    try {
        pre-commit install
        Write-Success "Pre-commit hooks installed"
    } catch {
        Write-Warning "Failed to install pre-commit hooks. You may need to install pre-commit manually."
    }
}

function Test-DependencyInjection {
    Write-Status "Testing dependency injection..."
    
    try {
        $testScript = @"
from src.infrastructure.dependency_injection import ApplicationContainer
container = ApplicationContainer()
config = container.config()
logger = container.logger()
print('✅ Dependency injection working correctly!')
"@
        
        python -c $testScript
        Write-Success "Dependency injection test passed"
    } catch {
        Write-Error "Dependency injection test failed: $_"
        throw
    }
}

function Invoke-InitialTests {
    if ($SkipTests) {
        Write-Warning "Skipping tests as requested"
        return
    }
    
    Write-Status "Running initial tests..."
    
    try {
        pytest tests/unit -v -m "unit"
        Write-Success "Initial tests passed"
    } catch {
        Write-Error "Initial tests failed: $_"
        throw
    }
}

function Set-IDEConfiguration {
    Write-Status "Setting up IDE configuration..."
    
    # Create .vscode directory if it doesn't exist
    if (-not (Test-Path ".vscode")) {
        New-Item -ItemType Directory -Path ".vscode" | Out-Null
    }
    
    # Create VS Code settings
    $vscodeSettings = @{
        "python.defaultInterpreterPath" = "./.venv/Scripts/python.exe"
        "python.linting.enabled" = $true
        "python.linting.flake8Enabled" = $true
        "python.linting.mypyEnabled" = $true
        "python.formatting.provider" = "black"
        "python.formatting.blackArgs" = @("--line-length=100")
        "python.sortImports.args" = @("--profile=black")
        "editor.formatOnSave" = $true
        "editor.codeActionsOnSave" = @{
            "source.organizeImports" = $true
        }
        "python.testing.pytestEnabled" = $true
        "python.testing.pytestArgs" = @("tests")
        "files.exclude" = @{
            "**/__pycache__" = $true
            "**/*.pyc" = $true
            ".pytest_cache" = $true
            ".mypy_cache" = $true
            ".coverage" = $true
            "htmlcov" = $true
        }
    }
    
    $vscodeSettings | ConvertTo-Json -Depth 10 | Out-File -FilePath ".vscode\settings.json" -Encoding UTF8
    Write-Success "VS Code configuration created"
}

function Show-NextSteps {
    Write-Host ""
    Write-Success "🎉 Development environment setup complete!"
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor $Colors.Cyan
    Write-Host "1. Edit .env file with your actual API keys"
    Write-Host "2. Activate virtual environment: .venv\Scripts\Activate.ps1"
    Write-Host "3. Run tests: make test (or pytest tests)"
    Write-Host "4. Start coding! 🚀"
    Write-Host ""
    Write-Host "Available commands:" -ForegroundColor $Colors.Cyan
    Write-Host "  make help          - Show all available commands"
    Write-Host "  make test          - Run all tests"
    Write-Host "  make test-unit     - Run unit tests only"
    Write-Host "  make lint          - Run code quality checks"
    Write-Host "  make format        - Format code"
    Write-Host ""
}

# Main execution
try {
    Write-Host ""
    Write-Status "🚀 Setting up Document Ingestion Pipeline Development Environment"
    Write-Host "=================================================================" -ForegroundColor $Colors.Blue
    Write-Host ""
    
    # Run setup steps
    if (-not (Test-PythonVersion)) {
        throw "Python 3.12 is required"
    }
    
    New-VirtualEnvironment
    Enable-VirtualEnvironment
    Install-Dependencies
    Set-EnvironmentFile
    Install-PreCommitHooks
    Test-DependencyInjection
    Invoke-InitialTests
    Set-IDEConfiguration
    
    Show-NextSteps
    
} catch {
    Write-Error "Setup failed: $_"
    exit 1
}
