# S3 Bucket Module with Lambda Integration

## Overview

The S3 bucket module has been enhanced to support Lambda integration while maintaining backward compatibility with existing CloudFront and website hosting use cases. This update enables seamless integration between S3 storage and Lambda functions for event-driven processing.

## Key Enhancements

### 1. Lambda Integration
- **S3 Event Notifications**: Automatic triggering of Lambda functions on S3 events
- **Configurable Filters**: Support for prefix and suffix filtering
- **Permission Management**: Automatic Lambda permission grants
- **Security**: SSL-only access enforcement

### 2. Lifecycle Management
- **Cost Optimization**: Automatic storage class transitions
- **Configurable Rules**: Flexible lifecycle policy configuration
- **Cleanup**: Automatic deletion and multipart upload cleanup

### 3. Enhanced Security
- **SSL Enforcement**: All requests must use HTTPS when Lambda integration is enabled
- **IAM Integration**: Proper role-based access control
- **Bucket Policies**: Enhanced security policies

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   S3 Bucket     │────│  Lambda Function│────│   External      │
│   (Storage)     │    │  (Processing)   │    │   Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────│   CloudWatch    │──────────────┘
                        │   (Monitoring)  │
                        └─────────────────┘
```

## Module Structure

### Updated Files

1. **`infra/modules/s3_bucket/main.tf`**
   - Added Lambda integration resources
   - Enhanced bucket policies
   - Lifecycle configuration support
   - Improved CORS settings

2. **`infra/modules/s3_bucket/vars.tf`**
   - Added `lambda_integration` variable
   - Added `lifecycle_rules` configuration
   - Added `tags` support
   - Added `enable_lifecycle` flag

3. **`infra/modules/s3_bucket/output.tf`**
   - Enhanced outputs for monitoring
   - Added Lambda integration status
   - Added lifecycle policy status

4. **`infra/modules/s3_bucket/README.md`**
   - Comprehensive documentation
   - Usage examples
   - Best practices

### Core Integration

**`infra/iac/core/s3.tf`**
- Updated to use enhanced S3 bucket module
- Configured for document ingestion pipeline
- Integrated with lambda platform outputs

## Usage Examples

### Document Ingestion Pipeline

```hcl
module "document_storage" {
  source = "../../modules/s3_bucket"

  bucket_name = "${var.env}-ezychat-documents"
  force_destroy = false

  # Lambda integration for document processing
  lambda_integration = {
    lambda_function_arn = module.lambda_platform.lambda_function_arns["document-ingestion-lambda"]
    lambda_function_name = module.lambda_platform.lambda_function_names["document-ingestion-lambda"]
    lambda_execution_role_arn = module.lambda_platform.lambda_execution_role_arns["document-ingestion-lambda"]
    events = ["s3:ObjectCreated:*"]
    filter_prefix = "*/uploaded/"
    filter_suffix = ".csv"
  }

  # Lifecycle policies for cost optimization
  enable_lifecycle = true
  lifecycle_rules = [
    {
      id = "document_lifecycle"
      status = "Enabled"
      transition = [
        { days = 30, storage_class = "STANDARD_IA" },
        { days = 90, storage_class = "GLACIER" }
      ]
      expiration = { days = 365 }
      abort_incomplete_multipart_upload = { days_after_initiation = 7 }
    }
  ]

  tags = {
    Environment = var.env
    Service     = "document-storage"
  }
}
```

### Static Website Hosting

```hcl
module "website_storage" {
  source = "../../modules/s3_bucket"

  bucket_name = "my-website"
  force_destroy = false

  cloudfront_origin_access_identity = "E1234567890ABC"

  website_configuration = {
    index_document_suffix = "index.html"
    error_document_key    = "error.html"
  }

  tags = {
    Environment = "production"
    Service     = "website"
  }
}
```

### Basic Storage Bucket

```hcl
module "basic_storage" {
  source = "../../modules/s3_bucket"

  bucket_name = "my-app-storage"
  force_destroy = false

  tags = {
    Environment = "production"
    Service     = "my-app"
  }
}
```

## Integration with Lambda Platform

### Lambda Platform Outputs

The lambda app platform module provides these outputs for S3 integration:

```hcl
module.lambda_platform.lambda_function_arns["service-name"]
module.lambda_platform.lambda_function_names["service-name"]
module.lambda_platform.lambda_execution_role_arns["service-name"]
```

### Template Variable Substitution

The lambda platform supports template variable substitution in IAM policies:

```yaml
# In lambda_services.yaml
document-ingestion-lambda:
  iam_policy: |
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
          "Resource": [
            "arn:aws:s3:::{{DOCUMENT_BUCKET_NAME}}",
            "arn:aws:s3:::{{DOCUMENT_BUCKET_NAME}}/*"
          ]
        }
      ]
    }
```

## Security Features

### 1. SSL Enforcement
When Lambda integration is enabled, the bucket enforces SSL-only access:

```json
{
  "Sid": "EnforceSSLOnly",
  "Effect": "Deny",
  "Principal": { "AWS": "*" },
  "Action": "s3:*",
  "Resource": ["arn:aws:s3:::bucket-name", "arn:aws:s3:::bucket-name/*"],
  "Condition": {
    "Bool": { "aws:SecureTransport": "false" }
  }
}
```

### 2. Lambda Access Control
Proper IAM permissions for Lambda functions:

```json
{
  "Sid": "AllowLambdaAccess",
  "Effect": "Allow",
  "Principal": { "AWS": "lambda-execution-role-arn" },
  "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket"],
  "Resource": ["arn:aws:s3:::bucket-name", "arn:aws:s3:::bucket-name/*"]
}
```

### 3. CORS Configuration
Enhanced CORS for web uploads:

```hcl
cors_rule {
  allowed_headers = ["*"]
  allowed_methods = ["GET", "PUT", "POST", "DELETE"]
  allowed_origins = ["*"]
  expose_headers  = ["ETag"]
  max_age_seconds = 3000
}
```

## Cost Optimization

### 1. Lifecycle Policies
Automatic storage class transitions:

- **30 days**: Move to Standard-IA
- **90 days**: Move to Glacier
- **365 days**: Automatic deletion
- **7 days**: Clean up incomplete multipart uploads

### 2. Storage Classes
- **Standard**: Frequently accessed data
- **Standard-IA**: Infrequently accessed data
- **Glacier**: Archival data

## Monitoring and Observability

### 1. CloudWatch Metrics
Monitor these metrics:
- S3 request counts and errors
- Lambda function performance
- Storage usage and costs

### 2. Outputs for Monitoring
```hcl
output "document_storage" {
  value = {
    bucket_name = module.document_storage.bucket_name
    bucket_arn = module.document_storage.bucket_arn
    lambda_integration_configured = module.document_storage.lambda_integration_configured
    lifecycle_enabled = module.document_storage.lifecycle_enabled
  }
}
```

## Migration Guide

### From Previous Version

1. **Existing Configurations**: Continue to work without changes
2. **Add Lambda Integration**: Use new `lambda_integration` variable
3. **Enable Lifecycle**: Use `enable_lifecycle` and `lifecycle_rules`
4. **Update Tags**: Use new `tags` variable for resource tagging

### Example Migration

**Before:**
```hcl
module "s3_bucket" {
  source = "../../modules/s3_bucket"
  bucket_name = "my-bucket"
  force_destroy = false
}
```

**After (with Lambda integration):**
```hcl
module "s3_bucket" {
  source = "../../modules/s3_bucket"
  bucket_name = "my-bucket"
  force_destroy = false
  
  lambda_integration = {
    lambda_function_arn = module.lambda_platform.lambda_function_arns["my-lambda"]
    lambda_function_name = module.lambda_platform.lambda_function_names["my-lambda"]
    lambda_execution_role_arn = module.lambda_platform.lambda_execution_role_arns["my-lambda"]
    events = ["s3:ObjectCreated:*"]
  }
  
  enable_lifecycle = true
  lifecycle_rules = [
    {
      id = "cost_optimization"
      status = "Enabled"
      transition = [
        { days = 30, storage_class = "STANDARD_IA" }
      ]
    }
  ]
  
  tags = {
    Environment = "production"
    Service = "my-app"
  }
}
```

## Best Practices

### 1. Naming Convention
- Use descriptive bucket names with environment prefixes
- Include project/application name in bucket name
- Use consistent tagging across resources

### 2. Security
- Always enable SSL enforcement for Lambda integration
- Use least privilege IAM policies
- Enable versioning for data protection
- Monitor access patterns and costs

### 3. Cost Management
- Enable lifecycle policies for cost optimization
- Monitor storage usage and access patterns
- Use appropriate storage classes
- Set up cost alerts and monitoring

### 4. Monitoring
- Set up CloudWatch metrics for bucket usage
- Monitor Lambda function performance
- Track storage costs and access patterns
- Use structured logging for debugging

## Troubleshooting

### Common Issues

1. **Lambda Permission Errors**
   - Verify Lambda function ARN is correct
   - Check IAM role permissions
   - Ensure S3 bucket policy allows Lambda access

2. **S3 Event Notifications Not Working**
   - Verify event configuration (prefix, suffix)
   - Check Lambda function exists and is accessible
   - Monitor CloudWatch logs for errors

3. **Lifecycle Policy Issues**
   - Verify rule configuration syntax
   - Check storage class names are correct
   - Monitor transition timing

### Debugging Steps

1. Check S3 bucket notification configuration
2. Verify Lambda function permissions
3. Monitor CloudWatch logs for errors
4. Test with small files first
5. Verify IAM policies and roles

## Conclusion

The enhanced S3 bucket module provides a robust, secure, and cost-effective solution for S3 storage with Lambda integration. It maintains backward compatibility while adding powerful new features for event-driven processing and cost optimization.

Key benefits:
- **Seamless Integration**: Easy integration with Lambda functions
- **Security First**: SSL enforcement and proper IAM controls
- **Cost Optimized**: Lifecycle policies and storage class management
- **Monitoring Ready**: Comprehensive outputs and observability
- **Backward Compatible**: Existing configurations continue to work 